const EXCLUDED_FIELDS = ['life_status', "lock_user", "is_deleted", "life_status_before_invalid", "object_describe_api_name", "package",
    "out_tenant_id", "version", "lock_rule", "object_describe_id", "extend_obj_data_id", "relevant_team", "is_deleted", "version", "create_time",
    "created_by", "last_modified_by", "last_modified_time", "create_time", "origin_source", "_id"];

const REQUIRED_EXCLUDED_FIELDS = ['owner'];

const options = {
    url: bundle.authData.baseUrl + '/cgi/crm/v2/object/describe',
    method: 'POST',
    body: {
        "includeDetail": true,
        "apiName": bundle.inputData.apiName
    }
}



return z.request(options)
    .then((response) => {
        const results = response.json;

        const formatLabel = (isRequired, label) => {
            return isRequired ? `${label}(required)` : label;
        };

        const formatType = (type) => {
            switch (type) {
                case 'date_time':
                    return 'datetime';
                case 'true_or_false':
                    return 'boolean';
                case 'date':
                    return 'datetime';
                default:
                    return 'string';
            }
        };

        const fieldsArray = Object.entries(results.data.describe.fields)
            .filter(([key, value]) => value.type !== 'auto_number')
            .filter(([key, value]) => !EXCLUDED_FIELDS.includes(key))
            .map(([key, value]) => {
                let isRequired = !REQUIRED_EXCLUDED_FIELDS.includes(key) && value.is_required;
                const fieldData = {
                    key: key,
                    label: formatLabel(isRequired, value.label),
                    helpText: value.description || key,
                    type: formatType(value.type)
                };
                if (value.type == 'select_many' || value.type == 'select_one') {
                    // 如果字段有options，转换为choices map格式
                    if (value.options && Array.isArray(value.options)) {
                        const choicesMap = {};
                        value.options.forEach(option => {
                            if (option.value && option.label) {
                                choicesMap[option.value] = option.label;
                            }
                        });
                        fieldData.choices = choicesMap;
                        if (value.type == 'select_many') {
                            //多选输入列表
                            fieldData.list = true;
                        }
                    }
                }

                if (value.type == 'record_type') {
                    fieldData.default = 'default__c';
                    // 如果字段有options，转换为choices map格式
                    if (value.options && Array.isArray(value.options)) {
                        const choicesMap = {};
                        value.options.forEach(option => {
                            if (option.api_name && option.label) {
                                choicesMap[option.api_name] = option.label;
                            }
                        });
                        fieldData.choices = choicesMap;
                    }
                }
                return fieldData;
            })
            .sort((a, b) => {
                const aIsRequired = a.label.includes('(required)');
                const bIsRequired = b.label.includes('(required)');
                if (aIsRequired && !bIsRequired) return -1;
                if (!aIsRequired && bIsRequired) return 1;
                return 0;
            });

        // 添加自定义字段
        const customFields = [
            {
                "key": "triggerApprovalFlow",
                "label": "Trigger Approval Flow",
                "helpText": "Whether the approval flow is triggered (the default value is true when it is not passed.)",
                "type": "boolean"
            }, {
                "key": "triggerWorkFlow",
                "label": "Trigger Work Flow",
                "helpText": "Whether the workflow is triggered (the default value is true when it is not transmitted, indicating that it is triggered.)",
                "type": "boolean"
            }, {
                "key": "hasSpecifyTime",
                "label": "Has Specify Time",
                "helpText": "Whether the creation time can be transmitted (the default value is false when the parameter is not transmitted, indicating that the create_time creation time field in the parameter is ignored)",
                "type": "boolean"
            }, {
                "key": "hasSpecifyCreatedBy",
                "label": "Has Specify Created By",
                "helpText": "Created by (false by default when not passed, which means that the created_by creation time field in the parameter is ignored)",
                "type": "boolean"
            }, {
                "key": "skipFuncValidate",
                "label": "Skip Function Validate",
                "helpText": "Whether to skip the function before verifying",
                "type": "boolean"
            }, {
                "key": "useValidationRule",
                "label": "Use Validation Rule",
                "helpText": "Whether the validation rule is triggered",
                "type": "boolean"
            }, {
                "key": "isDuplicateSearch",
                "label": "Is Duplicate Search",
                "helpText": "Whether to trigger a plagiarism check",
                "type": "boolean"
            }
        ];

        return [...fieldsArray, ...customFields];
    })
    .catch(error => {
        throw new Error(`Failed to process fields: ${error.message}`);
    });
