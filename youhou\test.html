<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confluence Helper 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: #0052cc;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 20px 0;
        }
        code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #0052cc;
        }
    </style>
</head>
<body>
    <!-- 模拟Confluence页面结构 -->
    <div data-space-key="TEST" data-page-id="123456">
        <div class="header">
            <h1>Confluence Helper 测试页面</h1>
            <p>这是一个用于测试Confluence Helper油猴脚本的模拟页面</p>
        </div>

        <div class="content">
            <h2>测试说明</h2>
            <p>本页面模拟了Confluence页面的基本结构，用于测试油猴脚本的功能。</p>
            
            <div class="info-box">
                <strong>注意：</strong> 这只是一个测试页面，实际的API调用需要在真实的Confluence环境中进行。
            </div>

            <h3>测试步骤</h3>
            
            <div class="step">
                <h3>1. 安装脚本</h3>
                <p>将 <code>confluenceHelper.js</code> 安装到Tampermonkey中</p>
            </div>

            <div class="step">
                <h3>2. 检查脚本加载</h3>
                <p>刷新页面后，应该能看到右上角的"Confluence助手"按钮</p>
            </div>

            <div class="step">
                <h3>3. 测试UI界面</h3>
                <p>点击按钮打开面板，检查各个标签页是否正常显示</p>
            </div>

            <div class="step">
                <h3>4. 配置测试</h3>
                <p>在配置页面填写信息（可以使用虚拟数据测试UI）</p>
            </div>

            <div class="step">
                <h3>5. 模板测试</h3>
                <p>在模板页面选择不同模板，检查内容是否正确加载</p>
            </div>

            <div class="warning-box">
                <strong>重要提醒：</strong> 
                <ul>
                    <li>实际的API测试需要在真实的Confluence页面进行</li>
                    <li>确保有有效的API令牌和适当的权限</li>
                    <li>建议先在测试环境中验证功能</li>
                </ul>
            </div>

            <h3>页面信息</h3>
            <ul>
                <li><strong>空间键:</strong> TEST (模拟)</li>
                <li><strong>页面ID:</strong> 123456 (模拟)</li>
                <li><strong>当前URL:</strong> <span id="current-url"></span></li>
            </ul>

            <h3>脚本功能检查清单</h3>
            <ul>
                <li>☐ 脚本成功加载</li>
                <li>☐ 助手按钮显示</li>
                <li>☐ 面板可以打开/关闭</li>
                <li>☐ 标签页切换正常</li>
                <li>☐ 配置表单可以填写</li>
                <li>☐ 模板选择功能正常</li>
                <li>☐ 内容输入区域正常</li>
                <li>☐ 预览功能正常</li>
            </ul>

            <h3>常见问题</h3>
            <div class="step">
                <h4>脚本没有加载？</h4>
                <p>检查Tampermonkey是否启用，脚本是否匹配当前页面URL</p>
            </div>

            <div class="step">
                <h4>按钮没有显示？</h4>
                <p>检查浏览器控制台是否有JavaScript错误</p>
            </div>

            <div class="step">
                <h4>样式显示异常？</h4>
                <p>可能是CSS冲突，检查页面的其他样式是否影响</p>
            </div>
        </div>

        <div class="content">
            <h2>开发者信息</h2>
            <p>如果您是开发者，可以打开浏览器控制台查看脚本的运行状态和调试信息。</p>
            
            <h3>调试技巧</h3>
            <ul>
                <li>使用 <code>console.log</code> 输出调试信息</li>
                <li>检查 <code>confluenceConfig</code> 对象的值</li>
                <li>验证页面元素选择器是否正确</li>
                <li>测试API请求的参数和响应</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示当前URL
        document.getElementById('current-url').textContent = window.location.href;
        
        // 模拟一些Confluence页面的行为
        console.log('测试页面已加载');
        console.log('空间键:', document.querySelector('[data-space-key]')?.getAttribute('data-space-key'));
        console.log('页面ID:', document.querySelector('[data-page-id]')?.getAttribute('data-page-id'));
    </script>
</body>
</html>
