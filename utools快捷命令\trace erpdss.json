{"program": "quickcommand", "features": {"explain": "trace erpdss", "platform": ["win32", "linux", "darwin"], "icon": "data:image/png;base64,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", "mainPush": false, "code": "key_1hs7x1mm50", "cmds": [{"label": "trace erpdss", "type": "regex", "match": "/^J-E(?:[^.]*\\.){4,}.*/", "minNum": 1}], "mainHide": true}, "output": "ignore", "tags": ["fxiaoke"], "cmd": "function parseSnowflakeID(snowflakeID) {\r\n  const EPOCH = 1288834974657n; // 起始时间戳转换为 BigInt\r\n\r\n  const timestampBits = 41n;\r\n  const dataCenterBits = 5n;\r\n  const workerBits = 5n;\r\n  const sequenceBits = 12n;\r\n\r\n  const maxSequence = (1n << sequenceBits) - 1n;\r\n  const maxWorkerId = (1n << workerBits) - 1n;\r\n  const maxDataCenterId = (1n << dataCenterBits) - 1n;\r\n\r\n  // 解析出各部分\r\n  const sequence = snowflakeID & maxSequence;\r\n  const workerId = (snowflakeID >> sequenceBits) & maxWorkerId;\r\n  const dataCenterId = (snowflakeID >> (sequenceBits + workerBits)) & maxDataCenterId;\r\n  const timestamp = (snowflakeID >> (sequenceBits + workerBits + dataCenterBits)) + EPOCH;\r\n\r\n  return {\r\n    timestamp: new Date(Number(timestamp)), // 将 BigInt 转换为 Number\r\n    dataCenterId: Number(dataCenterId), // 将 BigInt 转换为 Number\r\n    workerId: Number(workerId), // 将 BigInt 转换为 Number\r\n    sequence: Number(sequence) // 将 BigInt 转换为 Number\r\n  };\r\n}\r\n\r\nfunction base62ToBase10(base62Str) {\r\n    const characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\r\n    const base = 62;\r\n    let result = 0;\r\n  \r\n    for (let i = 0; i < base62Str.length; i++) {\r\n        const index = characters.indexOf(base62Str[i]);\r\n        if (index === -1) {\r\n            throw new Error('Invalid character found in the base62 string');\r\n        }\r\n        result = result * base + index;\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nfunction extractSnowflakeID(input) {\r\n    // 将字符串按照 '.' 拆分嘎\r\n    const parts = input.split('.');\r\n\r\n    // 检查点数，确保有足够的部分嘎\r\n    if (parts.length > 4) {\r\n      const base62Str = parts[4];\r\n        const base10Result = base62ToBase10(base62Str);\r\n        return base10Result.toString();\r\n    } else {\r\n        throw new Error('String does not contain enough parts');\r\n    }\r\n}\r\n\r\nvar traceId = \"{{payload}}\"\r\nconst snowflakeID = BigInt(extractSnowflakeID(traceId)); \r\nconst result = parseSnowflakeID(snowflakeID);\r\nconsole.log(`时间: ${result.timestamp}, 数据中心ID: ${result.dataCenterId}, 机器ID: ${result.workerId}, 序号: ${result.sequence}`);\r\nconst timestamp = result.timestamp.getTime();\r\n\r\n// 将时间戳转换为秒，往前1分钟，往后15分钟\r\nlet start = Math.floor((timestamp - 1 * 60 * 1000) / 1000);\r\nlet end = Math.floor((timestamp + 15 * 60 * 1000) / 1000);\r\n// 将时间戳转换为文本格式\r\nconsole.log(`timestamp:${timestamp},start:${start},end:${end}`)\r\n\r\nvar url = `https://log.foneshare.cn/query/?end=${end}&index=-1&kw=app='fs-erp-sync-data' and traceId like '${traceId}%'&logState=NaN&page=1&queryType=rawLog&size=10&start=${start}&tab=custom&tid=278`\r\n\r\nconsole.log(url)\r\n\r\n\r\nutools.shellOpenExternal(url)", "scptarg": "", "charset": {"scriptCode": "", "outputCode": ""}, "customOptions": {"bin": "", "argv": "", "ext": ""}, "hasSubInput": false, "cursorPosition": {"lineNumber": 64, "column": 60}}