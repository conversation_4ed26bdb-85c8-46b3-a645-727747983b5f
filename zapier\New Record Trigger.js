//直接查询最近2000条数据，如果，15分钟内，超过2000条，则会漏数据。
const options = {
    url: 'https://' + bundle.authData.domain + '/cgi/crm/custom/v2/data/findSimple',
    method: 'POST',
    body: {
        "data": {
            "igonreMediaIdConvert": true,
            "search_query_info": {
                "offset": 0,
                "limit": 2000,
                "orders": [
                    {
                        "fieldName": "last_modified_time",
                        "isAsc": false
                    }
                ],
            },
            "dataObjectApiName": bundle.inputData.apiName
        }
    },
    removeMissingValuesFrom: {
        'body': false,
        'params': false
    },
}

return z.request(options)
    .then((response) => {

        const data = JSON.parse(response.content);

        return data.data.dataList.map((item, index) => {
            if (item._id) {
                item.id = item._id;
            }
            return item;
        })
    });