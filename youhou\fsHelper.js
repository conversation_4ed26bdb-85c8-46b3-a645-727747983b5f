// ==UserScript==
// @name         纷享辅助
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  纷享辅助:显示id
// <AUTHOR>
// @match        *://*/*#crm/list*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    console.log('脚本开始执行'); // 添加脚本执行标记

    // 创建一个浮动面板来显示ID列表
    function createFloatingPanel() {
        console.log('创建浮动面板'); // 添加面板创建标记
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            max-height: 400px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            overflow-y: auto;
            z-index: 9999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;

        // 添加标题
        const title = document.createElement('h3');
        title.textContent = '数据ID列表';
        title.style.marginBottom = '10px';
        panel.appendChild(title);

        // 添加复制按钮
        const copyButton = document.createElement('button');
        copyButton.textContent = '复制所有ID';
        copyButton.style.marginBottom = '10px';
        copyButton.onclick = function() {
            const ids = Array.from(document.querySelectorAll('tr[data-id]'))
                .map(tr => tr.getAttribute('data-id'))
                .join('\n');
            navigator.clipboard.writeText(ids);
            alert('已复制所有ID到剪贴板！');
        };
        panel.appendChild(copyButton);

        // 创建ID列表容器
        const list = document.createElement('div');
        list.style.cssText = `
            font-family: monospace;
            font-size: 12px;
            line-height: 1.5;
        `;
        panel.appendChild(list);

        document.body.appendChild(panel);
        return list;
    }

    // 更新ID列表
    function updateIdList(container) {
        console.log('开始更新ID列表'); // 添加更新开始标记
        const rows = document.querySelectorAll('tr[data-id]');
        console.log('找到的行数:', rows.length);

        if (rows.length === 0) {
            container.innerHTML = '未找到带有data-id属性的行';
            return;
        }

        const idList = Array.from(rows).map((tr, index) => {
            const id = tr.getAttribute('data-id');
            console.log(`第${index + 1}行的ID:`, id);
            return `${index + 1}. ${id}`;
        });

        container.innerHTML = idList.join('<br>');
        console.log('ID列表更新完成'); // 添加更新完成标记
    }

    // 主函数
    function init() {
        console.log('初始化开始'); // 添加初始化标记
        const listContainer = createFloatingPanel();
        updateIdList(listContainer);

        // 添加定时检查，以防表格内容动态更新
        setInterval(() => {
            updateIdList(listContainer);
        }, 2000);
        console.log('初始化完成'); // 添加初始化完成标记
    }

    // 等待页面加载完成后执行
    function startScript() {
        console.log('准备启动脚本'); // 添加启动标记
        if (document.readyState === 'loading') {
            console.log('页面正在加载，等待DOMContentLoaded事件');
            document.addEventListener('DOMContentLoaded', init);
        } else {
            console.log('页面已加载完成，直接初始化');
            init();
        }
    }

    // 确保脚本在页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', startScript);
    } else {
        startScript();
    }
})();
