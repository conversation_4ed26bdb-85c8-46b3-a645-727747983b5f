// 添加 ISO 8601 日期格式检测和转换函数
function isISO8601(str) {
    const iso8601Regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?([+-]\d{2}:?\d{2}|Z)?$/;
    return iso8601Regex.test(str);
}

function convertToTimestamp(isoString) {
    return new Date(isoString).getTime();
}

// 递归处理对象中的所有属性
function processObject(obj) {
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            if (typeof obj[key] === 'string' && isISO8601(obj[key])) {
                obj[key] = convertToTimestamp(obj[key]);
            } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                processObject(obj[key]);
            }
        }
    }
    return obj;
}

// 处理 bundle.inputData
bundle.inputData = processObject(bundle.inputData);
// 处理 owner
bundle.inputData.owner = [bundle.authData.openUserId];
if (bundle.inputData.owner) {
    const ownerSet = new Set(Array.isArray(bundle.inputData.owner) ? bundle.inputData.owner : [bundle.inputData.owner]);
    bundle.inputData.owner = Array.from(ownerSet);
}

bundle.inputData.dataObjectApiName = bundle.inputData.apiName;
// delete bundle.inputData.company

const currentTime = Date.now();

const id = bundle.inputData._id;

let options;

const resultMap = {};

for (const key in bundle.inputData.details) {
    if (bundle.inputData.details.hasOwnProperty(key)) {
        try {
            const parsedValue = JSON.parse(bundle.inputData.details[key]);
            resultMap[key] = parsedValue;
        } catch (error) {
            throw new Error(`Error parsing JSON for key "${key}":`, error);
        }
    }
}

if (id) {
    const baseUrl = bundle.authData.baseUrl;
    const url = bundle.inputData.dataObjectApiName.endsWith('__c')
        ? baseUrl + '/cgi/crm/custom/v2/data/update'
        : baseUrl + '/cgi/crm/v2/data/update';

    options = {
        url: url,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        params: {

        },
        body: {
            "triggerApprovalFlow": bundle.inputData.triggerApprovalFlow,
            "triggerWorkFlow": bundle.inputData.triggerWorkFlow,
            "hasSpecifyTime": bundle.inputData.hasSpecifyTime,
            "hasSpecifyCreatedBy": bundle.inputData.hasSpecifyCreatedBy,
            "data": {
                "object_data": bundle.inputData,
                "optionInfo": {
                    "skipFuncValidate": bundle.inputData.skipFuncValidate,
                    "useValidationRule": bundle.inputData.useValidationRule,
                    "isDuplicateSearch": bundle.inputData.isDuplicateSearch
                }
            }
        },
        removeMissingValuesFrom: {
            'body': false,
            'params': false
        },
    }
} else {
    bundle.inputData.create_time = currentTime;
    const baseUrl = bundle.authData.baseUrl;
    const url = bundle.inputData.dataObjectApiName.endsWith('__c')
        ? baseUrl + '/cgi/crm/custom/v2/data/create'
        : baseUrl + '/cgi/crm/v2/data/create';

    options = {
        url: url,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        params: {

        },
        body: {
            "triggerApprovalFlow": bundle.inputData.triggerApprovalFlow,
            "triggerWorkFlow": bundle.inputData.triggerWorkFlow,
            "hasSpecifyTime": bundle.inputData.hasSpecifyTime,
            "hasSpecifyCreatedBy": bundle.inputData.hasSpecifyCreatedBy,
            "data": {
                "object_data": bundle.inputData,
                "optionInfo": {
                    "skipFuncValidate": bundle.inputData.skipFuncValidate,
                    "useValidationRule": bundle.inputData.useValidationRule,
                    "isDuplicateSearch": bundle.inputData.isDuplicateSearch
                }
            }
        },
        removeMissingValuesFrom: {
            'body': false,
            'params': false
        },
    }
}

return z.request(options)
    .then((response) => {
        const results = response.json;

        // You can do any parsing you need for results here before returning them

        return results;
    });