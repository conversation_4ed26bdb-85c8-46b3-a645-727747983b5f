import re
import os

def extract_environments(html_file):
    """从HTML文件中提取环境信息"""
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式提取环境信息
    pattern = r'<h3 id="([^"]+)">([^<]+)<a href="https?://([^"]+)"'  
    matches = re.findall(pattern, content)
    
    environments = []
    for match in matches:
        env_id, env_name, domain = match
        # 清理环境名称，去除多余的空格和括号内容
        env_name = env_name.strip()
        env_name = re.sub(r'\s+\([^)]+\)\s*$', '', env_name)
        
        # 提取域名
        domain = domain.strip('() ')
        
        # 构建URL
        auth_url = f"https://{domain}/erp/syncdata/noAuth/authToken?token={{superAdminToken}}"
        admin_url = f"https://{domain}/erp/syncdata/superadmin/opstool/index"
        
        environments.append({
            'name': env_name,
            'auth_url': auth_url,
            'admin_url': admin_url
        })
    
    return environments

def generate_markdown(environments, output_file):
    """生成Markdown格式的文档"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# 集成平台管理后台使用说明\n")
        f.write("## 如何修改该文档\n")
        f.write("在配置中，搜索类型为OPSTOOL_README的配置项，修改内容即可。内容为markdown格式，支持html解析。\n")
        f.write("## 专属云地址\n")
        f.write("先点击**获取cookie**\n")
        f.write("再点击**管理页面**\n")
        
        for env in environments:
            f.write(f"### {env['name']}\n")
            f.write(f"<a href=\"{env['auth_url']}\" target=\"_blank\">获取cookie</a>\n")
            f.write("<p>\n")
            f.write(f"<a href=\"{env['admin_url']}\" target=\"_blank\">管理页面</a>\n\n")

def main():
    script_dir = os.path.dirname(os.path.abspath(__file__))
    html_file = os.path.join(script_dir, 'oss.html')
    output_file = os.path.join(script_dir, 'generated_readme.md')
    
    environments = extract_environments(html_file)
    generate_markdown(environments, output_file)
    print(f"已生成Markdown文件: {output_file}")

if __name__ == "__main__":
    main()