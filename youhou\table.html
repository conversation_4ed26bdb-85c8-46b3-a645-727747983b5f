<div data-v-2951d5cc="" class="fx-frame-layout_col " style="width: 100%; min-width: 0px;"><div data-v-174b3289="" data-apiname="list_component" class="cs-running-component no-shadow"><div isrunning="true" componenttype="list" type="list" layoutid="67e68d1ffebfac0007842530" filterdata="" layoutapiname="67e68d1ffebfac0007842530" api_name="list_component" header="列表頁" namei18nkey="paas.udobj.list_page" view_info="[object Object],[object Object]" filters_info="[object Object]" button_info="[object Object],[object Object],[object Object]" define_view_info="list_view,split_view" scene_info="[object Object]" attributes="[object Object]" pagesource="objectList" objectapiname="object_j7X1y__c" source="list" sourceid="object_j7X1y__c" apiname="object_j7X1y__c" jspath="https://ceshi112.fspage.com/html/crm-dist/modules/page/list/list.js" param="object_j7X1y__c" pluginparams="[object Object]" disablelazyload="true"><div class="crm-module-content1"><div class="crm-table crm-table-noborder crm-table-open crm-widget crm-w-table crm-w-table20 crm-w-table-size2 crm-w-table-cellflex crm-w-table-noedit crm-table-md crm-table-nowrap crm-table-fixed"><!--
表格布局Layout
头部
检索
主题
分页
-->

<div class="dt-caption crm-clearfix clearfix">

<h2 class="dt-tit line">新对象</h2>


<div class="term-item">
<div class="term-con">全部<span class="fx-icon-arrow-down term-arrow-down"></span></div>
<div class="crm-widget crm-w-dropdown" style="display: none; width: 200px; position: absolute; z-index: 100; background: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 10px; border-radius: 2px;"><div class="wrap crm-scroll crm-scroll-nobar">
<ul class="list-menu">


<li class="list-item cur" data-isdef="1" data-key="1" data-request="0" data-id="5d0c806a7cfed91f3e95ba8e">
<span class="name">全部</span>

</li>



<li class="list-item " data-isdef="0" data-key="1" data-request="0" data-id="66dabb9ee6dd8500073e5d20">
<span class="name">我關注的</span>

</li>



<li class="list-item " data-isdef="0" data-key="1" data-request="0" data-id="5d0c806a7cfed91f3e95ba90">
<span class="name">我參與的</span>

</li>



<li class="list-item " data-isdef="0" data-key="1" data-request="0" data-id="5d0c806a7cfed91f3e95ba8f">
<span class="name">我負責的</span>

</li>



<li class="list-item " data-isdef="0" data-key="1" data-request="0" data-id="5d0c806a7cfed91f3e95ba92">
<span class="name">我下屬負責的</span>

</li>



<li class="list-item " data-isdef="0" data-key="1" data-request="0" data-id="5d0c806a7cfed91f3e95ba91">
<span class="name">我負責部門的</span>

</li>



<li class="list-item " data-isdef="0" data-key="1" data-request="0" data-id="5d0c806a7cfed91f3e95ba94">
<span class="name">共享給我的</span>

</li>



<li class="list-item " data-isdef="0" data-key="1" data-request="0" data-id="5d0c806a7cfed91f3e95ba93">
<span class="name">我下屬參與的</span>

</li>


</ul>
</div>

<div class="list-manage-wrap">
<ul class="list-menu">
<li class="term-add-btn"><span class="fx-icon-add-2 mr8"></span>新建篩選場景</li>
<li class="term-manage-btn"><span class="fx-icon-set mr8"></span>管理</li>
</ul>
</div>
</div></div>


<div class="dt-op-box"><div class="crm-btn-groups">


<div class="crm-btn-group">
<span data-maxwidth="120" data-pos="top" data-title="新建" class="crm-btn obtn20 crm-ui-title j-action crm-btn-primary " data-action="add">新建</span>
</div>



<div class="crm-btn-group">
<span class="crm-btn fx-icon-more show-fold "></span>
</div>

</div></div></div>

<div class="dt-term-batch">

<div class="batch-term crm-clearfix"><!--增加一个下拉菜单--><div class="item record-type crm-table-rt-hidden"><div class="item-con" style="width:160px"><div class="record-type crm-table-rt-hidden crm-widget crm-w-select crm-w-select-1"><div class="select-tit">
<div class="j-ipt-target ipt-target"></div>
<div class="tit-con crm-clearfix">

<input autocomplete="off" class="j-select-input" type="text" placeholder="">

</div>

<div class="select-icon fx-icon-arrow-up"></div>
<div class="more-btn crm-hide">...</div>
</div></div></div></div><div class="item target-item first-target-item crm-hide"></div>
<!--检索区域-->


<div class="crm-left filter-btn-wrap">
<span class="crm-btn term-filter-btn fx-icon-filter j-filter-btn"><span class="term-filter-name">篩選</span><span class="term-filter-num"><em class="ne">0</em><span class="nc el-icon-close j-clean-all"></span></span></span>
</div>


<div class="crm-left column-set-wrap" style="width:0px;overflow:hidden;">
<span class="crm-btn term-column-btn j-tb-setcolumn"></span>
</div>

<div class="dt-sc-box j-dt-sc-box crm-left"><div class="crm-w-table-search-wrap"><div class="crm-w-table-search"><div class="fx-select"><div class="crm-widget crm-w-select crm-w-select-2"><div class="select-tit">
<div class="j-ipt-target ipt-target"></div>
<div class="tit-con crm-clearfix">

<input autocomplete="off" class="j-select-input" type="text" placeholder="">

</div>

<div class="select-icon fx-icon-arrow-up"></div>
<div class="more-btn crm-hide">...</div>
</div></div></div> <div class="s-line"></div> <div class="dt-ipt-wrap"><form autocomplete="off" onsubmit="return false;"><div class="el-input fx-input el-input--mini el-input--suffix"><!----> <input type="text" autocomplete="off" placeholder="搜索主屬性 " maxlength="100" class="el-input__inner dt-ipt"> <!----> <!----> <!----> <!----> <!----></div></form></div> <span class="dt-sc-ico dt-sc-btn font-search">搜索</span></div></div></div>
<!--增加控件参照节点-->
<div class="last-target-item crm-hide"></div>
<div class="dt-control-btns">

<span data-pos="top" data-title="設定" class="crm-ui-title j-dt-icon-tableconfig fx-icon-set"></span>
<span data-pos="top" data-title="設定" class="crm-ui-title dt-icon-columnconfig j-dt-icon-columnconfig fx-icon-set"></span>


<span data-pos="top" data-title="刷新" class="crm-ui-title j-dt-icon-refresh fx-icon-refresh"></span>

</div>

<div class="item other-item">
<div class="item-con other-con crm-btn-group">

<span data-pos="top" class="crm-btn fx-icon-list-2 list-con j-btn-guide os-list-btn cur crm-ui-title" data-type="list" data-title="列表視圖"><em>列表視圖</em></span>

<span data-pos="top" class="crm-btn fx-icon-view-splitscreen j-btn-guide os-split-btn crm-ui-title" data-type="split" data-title="分屏視圖"><em>分屏視圖</em></span>

</div>
</div>
</div>
</div>


<div class="dt-out-filter"><div class="crm-tb-outfilter">

<div class="inner">
<div class="o-content">
<div class="o-filter-wrap">
<div class="o-filter">
</div>
</div>
<div class="btn-wrap">
<div style="position: relative;z-index: 5;display: inline;">
<span class="crm-btn crm-btn-sm crm-btn-primary j-out-filter">篩選</span>
<span class="crm-btn crm-btn-sm j-out-clean">清除外露篩選值</span><span class="about-num"> ( 約1000+條</span><span class="about-num-wrap"><span class="look-num j-look-num">精確總數</span> )</span>
</div>
<div style="z-index: 1;" class="more-btn-wrap">
<span style="display: inline-flex;height: 100%;align-items: center;" class="j-cols">
<span class="s-more">查看更多</span>
<span class="s-col">收起</span>
<span class="s-arrow fx-icon-arrow-down2"></span>
</span>
</div>
</div>
</div>
</div>
</div></div>
<div class="dt-batchoperate-wrapper"><div class="item batch-item" style="display: none;"><span class="item-tit j-show__allchecked">已選擇<em>0</em>條</span> <span title="關閉" class="dt-close-batch el-icon-close"></span> <div class="item-con batch-con"><div class="crm-comp-fg">
<div class="fg-inner">

<div data-action="BulkHangTag" class="fg-item"><span title="打標籤" class="fg-v">打標籤</span></div>

<div data-action="AsyncBulkChangeOwner" class="fg-item"><span title="更換負責人" class="fg-v">更換負責人</span></div>

<div data-action="AsyncBulkInvalid" class="fg-item"><span title="作廢" class="fg-v">作廢</span></div>

<div data-action="AsyncBulkAddTeamMember" class="fg-item"><span title="添加相關團隊成員" class="fg-v">添加相關團隊成員</span></div>

<div data-action="AsyncBulkDeleteTeamMember" class="fg-item"><span title="移除相關團隊成員" class="fg-v">移除相關團隊成員</span></div>

<div data-action="AsyncBulkLock" class="fg-item"><span title="鎖定" class="fg-v">鎖定</span></div>

<div data-action="AsyncBulkUnlock" class="fg-item"><span title="解鎖" class="fg-v">解鎖</span></div>

<div data-action="Export" class="fg-item"><span title="導出" class="fg-v">導出</span></div>

<div data-action="ExportFile" class="fg-item"><span title="導出圖片/附件" class="fg-v">導出圖片/附件</span></div>

<div data-action="AsyncBulkFollow" class="fg-item"><span title="關注" class="fg-v">關注</span></div>

<div data-action="AsyncBulkUnfollow" class="fg-item"><span title="取消關注" class="fg-v">取消關注</span></div>

<div data-action="AsyncBulkPrint" class="fg-item"><span title="列印" class="fg-v">列印</span></div>

<div data-action="" class="fg-item fg-more" style="display: none;"><span title="..." class="fg-v">...</span></div>

</div>
</div></div></div></div>
<div class="dt-main"><!-- 表头 -->
<div class="header crm-clearfix">

<div class="fix-start-b" style="width: 148px;">
<table class="j-header-fixed">
<thead>
<tr>


<th class="th-cc th-null column-checkbox" data-name="" width="46px;">
<div class="tb-cell">



<em class="checkbox-item j-all-checkbox"></em>







</div>
</th>


<th class="th-cc th-name th-sort th-filter" data-name="name" width="100px;">
<div class="tb-cell">



<span title="主屬性 " class="icon-title">主屬性 </span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="解鎖" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>

</tr>
</thead>
</table>
</div>
<div class="tb-b j-tb-header" tabindex="1000" onmousewheel="return false;" style="margin-right: 0px;">
<div class="tb-b-i">
<table class="tb j-header-tb" style="width: 2787px;">
<thead>
<tr>

<th class="j-sfixed-th" width="148px;"></th>




<th class="th-cc th-field_dh25b__c th-sort th-filter" data-name="field_dH25b__c" width="220px;">
<div class="tb-cell">



<span title="ERP编码" class="icon-title">ERP编码</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-field_uqrv1__c th-sort th-filter" data-name="field_uQRv1__c" width="220px;">
<div class="tb-cell">



<span title="ERPID" class="icon-title">ERPID</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-field_product__c th-sort th-filter" data-name="field_product__c" width="150px;">
<div class="tb-cell">



<span title="产品" class="icon-title">产品</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-field_reference_field__c th-sort th-filter" data-name="field_reference_field__c" width="230px;">
<div class="tb-cell">



<span title="物料名称（落地）" class="icon-title">物料名称（落地）</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-field_prfnv__c" data-name="field_PrFnv__c" width="112px;">
<div class="tb-cell">



<span title="产品价格（不落地）" class="icon-title">产品价格（不落地）</span>




<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>



<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-field_gvx7z__c th-sort th-filter" data-name="field_GVX7z__c" width="112px;">
<div class="tb-cell">



<span title="引用布尔值(落地）" class="icon-title">引用布尔值(落地）</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-field_s3ppj__c th-sort th-filter" data-name="field_S3ppj__c" width="220px;">
<div class="tb-cell">



<span title="备注" class="icon-title">备注</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-record_type" data-name="record_type" width="112px;">
<div class="tb-cell">



<span title="業務類型" class="icon-title">業務類型</span>




<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>



<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-life_status th-sort th-filter" data-name="life_status" width="112px;">
<div class="tb-cell">



<span title="生命狀態" class="icon-title">生命狀態</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-data_own_department th-sort th-filter" data-name="data_own_department" width="112px;">
<div class="tb-cell">



<span title="歸屬部門" class="icon-title">歸屬部門</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-out_owner th-sort th-filter" data-name="out_owner" width="100px;">
<div class="tb-cell">



<span title="外部負責人" class="icon-title">外部負責人</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-lock_status th-sort th-filter" data-name="lock_status" width="112px;">
<div class="tb-cell">



<span title="鎖定狀態" class="icon-title">鎖定狀態</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-relevant_team th-sort th-filter" data-name="relevant_team" width="100px;">
<div class="tb-cell">



<span title="相關團隊" class="icon-title">相關團隊</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-owner th-sort th-filter" data-name="owner" width="100px;">
<div class="tb-cell">



<span title="負責人" class="icon-title">負責人</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-owner_department th-filter" data-name="owner_department" width="112px;">
<div class="tb-cell">



<span title="負責人主屬部門" class="icon-title">負責人主屬部門</span>




<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-created_by th-sort th-filter" data-name="created_by" width="100px;">
<div class="tb-cell">



<span title="創建人" class="icon-title">創建人</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-create_time th-sort th-filter" data-name="create_time" width="116px;">
<div class="tb-cell">



<span title="創建時間" class="icon-title">創建時間</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-last_modified_by th-sort th-filter" data-name="last_modified_by" width="100px;">
<div class="tb-cell">



<span title="最後修改人" class="icon-title">最後修改人</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>


<span class="resize-line"></span>
</div>
</th>


<th class="th-cc th-last_modified_time th-sort th-filter j-last-th th-sort-desc" data-name="last_modified_time" width="116px;" style="width: 116px;">
<div class="tb-cell">



<span title="最後修改時間" class="icon-title">最後修改時間</span>



<span data-pos="top" data-title="排序" class="ico-sort crm-ui-title" data-sortval="1,2"></span>


<span data-pos="top" data-title="鎖定" class="crm-ui-title ico-th ico-lock j-th-lock fx-icon-lock"></span>


<span data-pos="top" data-title="篩選" class="crm-ui-title ico-operate fx-icon-filter ico-th j-th-filter"></span>



</div>
</th>


<th class="j-efixed-th" width="65px;"></th>

</tr>
</thead>
</table>
</div>
</div>
<div class="fix-end-b" style="width: 65px; right: 0px;">
<table class="j-header-fixed">
<thead>
<tr>

<th class="th-null column-operate  " data-name="" width="64px;">
<div class="tb-cell">


操作



</div>
</th>

</tr>
</thead>
</table>
</div>

</div><!--表数据--><div class="main main-tr-handle" tabindex="-9" style="height:auto; max-height: none">
<div class="fix-start-b" style="width: 148px; height: 680px;">
<table>
<thead>
<tr>

<th class="th-null column-checkbox" data-name="" width="46px;"></th>

<th class="th-name" data-name="name" width="100px;"></th>

</tr>
</thead>
<tbody><tr style="" class="tr" data-index="0" data-id="67e52a3a46dc8000068dcbf1" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="67e52a3a46dc8000068dcbf1" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10220">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="67e52a3a46dc8000068dcbf1" class="tb-cell j-td main755-name td-cell__16" title="10220">10220<a href="/XV/UI/Home#objdetail/object_j7X1y__c/67e52a3a46dc8000068dcbf1" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="1" data-id="67e5219846dc8000068d6a03" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="67e5219846dc8000068d6a03" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10218">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="67e5219846dc8000068d6a03" class="tb-cell j-td main755-name td-cell__16" title="10218">10218<a href="/XV/UI/Home#objdetail/object_j7X1y__c/67e5219846dc8000068d6a03" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="2" data-id="67e521a146dc8000068d6bd7" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="67e521a146dc8000068d6bd7" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10219">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="67e521a146dc8000068d6bd7" class="tb-cell j-td main755-name td-cell__16" title="10219">10219<a href="/XV/UI/Home#objdetail/object_j7X1y__c/67e521a146dc8000068d6bd7" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="3" data-id="6717524d2743a300014af496" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="6717524d2743a300014af496" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10217">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="6717524d2743a300014af496" class="tb-cell j-td main755-name td-cell__16" title="10217">10217<a href="/XV/UI/Home#objdetail/object_j7X1y__c/6717524d2743a300014af496" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="4" data-id="671711cb2743a3000147175a" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="671711cb2743a3000147175a" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10216">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="671711cb2743a3000147175a" class="tb-cell j-td main755-name td-cell__16" title="10216">10216<a href="/XV/UI/Home#objdetail/object_j7X1y__c/671711cb2743a3000147175a" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="5" data-id="671647ee2743a3000143e105" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="671647ee2743a3000143e105" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10215">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="671647ee2743a3000143e105" class="tb-cell j-td main755-name td-cell__16" title="10215">10215<a href="/XV/UI/Home#objdetail/object_j7X1y__c/671647ee2743a3000143e105" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="6" data-id="671642d72743a3000143459d" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="671642d72743a3000143459d" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10214">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="671642d72743a3000143459d" class="tb-cell j-td main755-name td-cell__16" title="10214">10214<a href="/XV/UI/Home#objdetail/object_j7X1y__c/671642d72743a3000143459d" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="7" data-id="670a307f85821400010f6858" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a307f85821400010f6858" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10213">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="670a307f85821400010f6858" class="tb-cell j-td main755-name td-cell__16" title="10213">10213<a href="/XV/UI/Home#objdetail/object_j7X1y__c/670a307f85821400010f6858" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="8" data-id="670a304785821400010f5d22" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a304785821400010f5d22" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10212">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="670a304785821400010f5d22" class="tb-cell j-td main755-name td-cell__16" title="10212">10212<a href="/XV/UI/Home#objdetail/object_j7X1y__c/670a304785821400010f5d22" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="9" data-id="670a2fd585821400010f35b4" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a2fd585821400010f35b4" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10211">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="670a2fd585821400010f35b4" class="tb-cell j-td main755-name td-cell__16" title="10211">10211<a href="/XV/UI/Home#objdetail/object_j7X1y__c/670a2fd585821400010f35b4" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="10" data-id="670a2ea485821400010f2122" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a2ea485821400010f2122" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10210">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="670a2ea485821400010f2122" class="tb-cell j-td main755-name td-cell__16" title="10210">10210<a href="/XV/UI/Home#objdetail/object_j7X1y__c/670a2ea485821400010f2122" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="11" data-id="670a2e4d85821400010f1b4b" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a2e4d85821400010f1b4b" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10209">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="670a2e4d85821400010f1b4b" class="tb-cell j-td main755-name td-cell__16" title="10209">10209<a href="/XV/UI/Home#objdetail/object_j7X1y__c/670a2e4d85821400010f1b4b" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="12" data-id="670a2dd285821400010f14ec" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a2dd285821400010f14ec" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10208">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="670a2dd285821400010f14ec" class="tb-cell j-td main755-name td-cell__16" title="10208">10208<a href="/XV/UI/Home#objdetail/object_j7X1y__c/670a2dd285821400010f14ec" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="13" data-id="670a16e585821400010d0354" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a16e585821400010d0354" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10207">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="670a16e585821400010d0354" class="tb-cell j-td main755-name td-cell__16" title="10207">10207<a href="/XV/UI/Home#objdetail/object_j7X1y__c/670a16e585821400010d0354" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="14" data-id="670a16ac85821400010cf834" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a16ac85821400010cf834" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10206">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="670a16ac85821400010cf834" class="tb-cell j-td main755-name td-cell__16" title="10206">10206<a href="/XV/UI/Home#objdetail/object_j7X1y__c/670a16ac85821400010cf834" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="15" data-id="66beb548f7f27300010890db" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66beb548f7f27300010890db" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10005">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="66beb548f7f27300010890db" class="tb-cell j-td main755-name td-cell__16" title="10005">10005<a href="/XV/UI/Home#objdetail/object_j7X1y__c/66beb548f7f27300010890db" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="16" data-id="66bca3cdf7f2730001e9ff89" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66bca3cdf7f2730001e9ff89" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10004">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="66bca3cdf7f2730001e9ff89" class="tb-cell j-td main755-name td-cell__16" title="10004">10004<a href="/XV/UI/Home#objdetail/object_j7X1y__c/66bca3cdf7f2730001e9ff89" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="17" data-id="66bca3cdf7f2730001e9ff88" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66bca3cdf7f2730001e9ff88" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10003">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="66bca3cdf7f2730001e9ff88" class="tb-cell j-td main755-name td-cell__16" title="10003">10003<a href="/XV/UI/Home#objdetail/object_j7X1y__c/66bca3cdf7f2730001e9ff88" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="18" data-id="66bca3cdf7f2730001e9ff87" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66bca3cdf7f2730001e9ff87" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10002">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="66bca3cdf7f2730001e9ff87" class="tb-cell j-td main755-name td-cell__16" title="10002">10002<a href="/XV/UI/Home#objdetail/object_j7X1y__c/66bca3cdf7f2730001e9ff87" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr><tr style="" class="tr" data-index="19" data-id="66bca3cdf7f2730001e9ff86" data-ld="loaded">





<td style="" class="td-null column-checkbox" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66bca3cdf7f2730001e9ff86" class="tb-cell"><em class="checkbox-item"></em></div>


</td>



<td style="" class="td-name" title="10001">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="name" data-id="66bca3cdf7f2730001e9ff86" class="tb-cell j-td main755-name td-cell__16" title="10001">10001<a href="/XV/UI/Home#objdetail/object_j7X1y__c/66bca3cdf7f2730001e9ff86" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



</tr></tbody>
</table>
</div>
<div class="main-con main-scroll" style="max-height: none">
<div class="j-count-scroll-width count-scroll-width" style=""></div>
<table class="tb" style="width:2787px;">
<thead>
<tr>

<th class="j-sfixed-th" width="148px;"></th>


<th class="th-field_dh25b__c" data-name="field_dH25b__c" width="220px;"></th>

<th class="th-field_uqrv1__c" data-name="field_uQRv1__c" width="220px;"></th>

<th class="th-field_product__c" data-name="field_product__c" width="150px;"></th>

<th class="th-field_reference_field__c" data-name="field_reference_field__c" width="230px;"></th>

<th class="th-field_prfnv__c" data-name="field_PrFnv__c" width="112px;"></th>

<th class="th-field_gvx7z__c" data-name="field_GVX7z__c" width="112px;"></th>

<th class="th-field_s3ppj__c" data-name="field_S3ppj__c" width="220px;"></th>

<th class="th-record_type" data-name="record_type" width="112px;"></th>

<th class="th-life_status" data-name="life_status" width="112px;"></th>

<th class="th-data_own_department" data-name="data_own_department" width="112px;"></th>

<th class="th-out_owner" data-name="out_owner" width="100px;"></th>

<th class="th-lock_status" data-name="lock_status" width="112px;"></th>

<th class="th-relevant_team" data-name="relevant_team" width="100px;"></th>

<th class="th-owner" data-name="owner" width="100px;"></th>

<th class="th-owner_department" data-name="owner_department" width="112px;"></th>

<th class="th-created_by" data-name="created_by" width="100px;"></th>

<th class="th-create_time" data-name="create_time" width="116px;"></th>

<th class="th-last_modified_by" data-name="last_modified_by" width="100px;"></th>

<th class="th-last_modified_time j-last-th" data-name="last_modified_time" width="116px;" style="width: 116px;"></th>


<th class="j-efixed-th" width="65px;"></th>

</tr>
</thead>
<tbody><tr style="" class="tr" data-index="0" data-id="67e52a3a46dc8000068dcbf1" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td td-cell__16">--</div>


</td>



<td style="" class="td-field_reference_field__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_s3ppj__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="67e52a3a46dc8000068dcbf1" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="67e52a3a46dc8000068dcbf1" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="67e52a3a46dc8000068dcbf1" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="67e52a3a46dc8000068dcbf1" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="67e52a3a46dc8000068dcbf1" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="1" data-id="67e5219846dc8000068d6a03" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="67e5219846dc8000068d6a03" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="67e5219846dc8000068d6a03" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="67e5219846dc8000068d6a03" title="" class="tb-cell j-td td-cell__16">--</div>


</td>



<td style="" class="td-field_reference_field__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="67e5219846dc8000068d6a03" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="67e5219846dc8000068d6a03" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="67e5219846dc8000068d6a03" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="67e5219846dc8000068d6a03" title="2" class="tb-cell j-td">2</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="67e5219846dc8000068d6a03" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="67e5219846dc8000068d6a03" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="67e5219846dc8000068d6a03" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="67e5219846dc8000068d6a03" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="67e5219846dc8000068d6a03" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="67e5219846dc8000068d6a03" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="67e5219846dc8000068d6a03" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="2" data-id="67e521a146dc8000068d6bd7" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="67e521a146dc8000068d6bd7" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="67e521a146dc8000068d6bd7" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="67e521a146dc8000068d6bd7" title="" class="tb-cell j-td td-cell__16">--</div>


</td>



<td style="" class="td-field_reference_field__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="67e521a146dc8000068d6bd7" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="67e521a146dc8000068d6bd7" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="67e521a146dc8000068d6bd7" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="67e521a146dc8000068d6bd7" title="1" class="tb-cell j-td">1</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="67e521a146dc8000068d6bd7" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="67e521a146dc8000068d6bd7" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="67e521a146dc8000068d6bd7" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="67e521a146dc8000068d6bd7" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="67e521a146dc8000068d6bd7" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="67e521a146dc8000068d6bd7" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="67e521a146dc8000068d6bd7" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="3" data-id="6717524d2743a300014af496" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="6717524d2743a300014af496" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="6717524d2743a300014af496" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="6717524d2743a300014af496" title="" class="tb-cell j-td td-cell__16">--</div>


</td>



<td style="" class="td-field_reference_field__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="6717524d2743a300014af496" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="6717524d2743a300014af496" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="6717524d2743a300014af496" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="6717524d2743a300014af496" title="sdf555" class="tb-cell j-td">sdf555</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="6717524d2743a300014af496" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="6717524d2743a300014af496" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="6717524d2743a300014af496" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="6717524d2743a300014af496" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="6717524d2743a300014af496" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="6717524d2743a300014af496" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="6717524d2743a300014af496" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="4" data-id="671711cb2743a3000147175a" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="671711cb2743a3000147175a" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="671711cb2743a3000147175a" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="671711cb2743a3000147175a" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="671711cb2743a3000147175a" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="671711cb2743a3000147175a" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="671711cb2743a3000147175a" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="671711cb2743a3000147175a" title="3232322" class="tb-cell j-td">3232322</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="671711cb2743a3000147175a" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="671711cb2743a3000147175a" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="671711cb2743a3000147175a" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="671711cb2743a3000147175a" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="671711cb2743a3000147175a" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="671711cb2743a3000147175a" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="671711cb2743a3000147175a" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="5" data-id="671647ee2743a3000143e105" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="671647ee2743a3000143e105" title="10215" class="tb-cell j-td">10215</div>


</td>



<td style="" class="td-field_uqrv1__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="671647ee2743a3000143e105" title="671647f58022a0" class="tb-cell j-td">671647f58022a0</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="671647ee2743a3000143e105" title="CH4474#721物料001" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="66040d3e44bd6e00074fcd8a" data-apiname="ProductObj">CH4474#721物料001</a><a href="/XV/UI/Home#objdetail/ProductObj/66040d3e44bd6e00074fcd8a" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="671647ee2743a3000143e105" title="721物料001---" class="tb-cell j-td">721物料001---</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="671647ee2743a3000143e105" title="" class="tb-cell j-td">345.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="671647ee2743a3000143e105" title="" class="tb-cell j-td">是</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="671647ee2743a3000143e105" title="222333www" class="tb-cell j-td">222333www</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="671647ee2743a3000143e105" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="671647ee2743a3000143e105" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="671647ee2743a3000143e105" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="671647ee2743a3000143e105" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="671647ee2743a3000143e105" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="671647ee2743a3000143e105" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="671647ee2743a3000143e105" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="6" data-id="671642d72743a3000143459d" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="671642d72743a3000143459d" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="671642d72743a3000143459d" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="671642d72743a3000143459d" title="" class="tb-cell j-td td-cell__16">--</div>


</td>



<td style="" class="td-field_reference_field__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="671642d72743a3000143459d" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="671642d72743a3000143459d" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="671642d72743a3000143459d" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="671642d72743a3000143459d" title="111" class="tb-cell j-td">111</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="671642d72743a3000143459d" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="671642d72743a3000143459d" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="671642d72743a3000143459d" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="671642d72743a3000143459d" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="671642d72743a3000143459d" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="671642d72743a3000143459d" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="671642d72743a3000143459d" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="7" data-id="670a307f85821400010f6858" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="670a307f85821400010f6858" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="670a307f85821400010f6858" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="670a307f85821400010f6858" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="670a307f85821400010f6858" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="670a307f85821400010f6858" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="670a307f85821400010f6858" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="670a307f85821400010f6858" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="670a307f85821400010f6858" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="670a307f85821400010f6858" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="670a307f85821400010f6858" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="670a307f85821400010f6858" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="670a307f85821400010f6858" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="670a307f85821400010f6858" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="670a307f85821400010f6858" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="8" data-id="670a304785821400010f5d22" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="670a304785821400010f5d22" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="670a304785821400010f5d22" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="670a304785821400010f5d22" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="670a304785821400010f5d22" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="670a304785821400010f5d22" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="670a304785821400010f5d22" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="670a304785821400010f5d22" title="导入10000" class="tb-cell j-td">导入10000</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="670a304785821400010f5d22" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="670a304785821400010f5d22" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="670a304785821400010f5d22" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="670a304785821400010f5d22" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="670a304785821400010f5d22" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="670a304785821400010f5d22" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="670a304785821400010f5d22" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="9" data-id="670a2fd585821400010f35b4" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="670a2fd585821400010f35b4" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="670a2fd585821400010f35b4" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="670a2fd585821400010f35b4" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="670a2fd585821400010f35b4" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="670a2fd585821400010f35b4" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="670a2fd585821400010f35b4" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="670a2fd585821400010f35b4" title="导入10000" class="tb-cell j-td">导入10000</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="670a2fd585821400010f35b4" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="670a2fd585821400010f35b4" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="670a2fd585821400010f35b4" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="670a2fd585821400010f35b4" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="670a2fd585821400010f35b4" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="670a2fd585821400010f35b4" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="670a2fd585821400010f35b4" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="10" data-id="670a2ea485821400010f2122" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="670a2ea485821400010f2122" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="670a2ea485821400010f2122" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="670a2ea485821400010f2122" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="670a2ea485821400010f2122" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="670a2ea485821400010f2122" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="670a2ea485821400010f2122" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="670a2ea485821400010f2122" title="导入10000" class="tb-cell j-td">导入10000</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="670a2ea485821400010f2122" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="670a2ea485821400010f2122" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="670a2ea485821400010f2122" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="670a2ea485821400010f2122" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="670a2ea485821400010f2122" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="670a2ea485821400010f2122" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="670a2ea485821400010f2122" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="11" data-id="670a2e4d85821400010f1b4b" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="670a2e4d85821400010f1b4b" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="670a2e4d85821400010f1b4b" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="670a2e4d85821400010f1b4b" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="670a2e4d85821400010f1b4b" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="670a2e4d85821400010f1b4b" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="670a2e4d85821400010f1b4b" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="670a2e4d85821400010f1b4b" title="导入10000" class="tb-cell j-td">导入10000</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="670a2e4d85821400010f1b4b" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="670a2e4d85821400010f1b4b" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="670a2e4d85821400010f1b4b" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="670a2e4d85821400010f1b4b" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="670a2e4d85821400010f1b4b" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="670a2e4d85821400010f1b4b" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="670a2e4d85821400010f1b4b" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="12" data-id="670a2dd285821400010f14ec" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="670a2dd285821400010f14ec" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="670a2dd285821400010f14ec" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="670a2dd285821400010f14ec" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="670a2dd285821400010f14ec" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="670a2dd285821400010f14ec" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="670a2dd285821400010f14ec" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="670a2dd285821400010f14ec" title="导入10000" class="tb-cell j-td">导入10000</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="670a2dd285821400010f14ec" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="670a2dd285821400010f14ec" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="670a2dd285821400010f14ec" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="670a2dd285821400010f14ec" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="670a2dd285821400010f14ec" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="670a2dd285821400010f14ec" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="670a2dd285821400010f14ec" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="13" data-id="670a16e585821400010d0354" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="670a16e585821400010d0354" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="670a16e585821400010d0354" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="670a16e585821400010d0354" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="670a16e585821400010d0354" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="670a16e585821400010d0354" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="670a16e585821400010d0354" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="670a16e585821400010d0354" title="导入10000" class="tb-cell j-td">导入10000</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="670a16e585821400010d0354" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="670a16e585821400010d0354" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="670a16e585821400010d0354" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="670a16e585821400010d0354" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="670a16e585821400010d0354" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="670a16e585821400010d0354" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="670a16e585821400010d0354" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="14" data-id="670a16ac85821400010cf834" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="670a16ac85821400010cf834" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="670a16ac85821400010cf834" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="670a16ac85821400010cf834" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="670a16ac85821400010cf834" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="670a16ac85821400010cf834" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="670a16ac85821400010cf834" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="670a16ac85821400010cf834" title="导入10000" class="tb-cell j-td">导入10000</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="670a16ac85821400010cf834" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="670a16ac85821400010cf834" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="670a16ac85821400010cf834" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="670a16ac85821400010cf834" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="670a16ac85821400010cf834" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="670a16ac85821400010cf834" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="670a16ac85821400010cf834" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="15" data-id="66beb548f7f27300010890db" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="66beb548f7f27300010890db" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="66beb548f7f27300010890db" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="66beb548f7f27300010890db" title="CH4474#721物料001" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="66040d3e44bd6e00074fcd8a" data-apiname="ProductObj">CH4474#721物料001</a><a href="/XV/UI/Home#objdetail/ProductObj/66040d3e44bd6e00074fcd8a" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="66beb548f7f27300010890db" title="721物料001---" class="tb-cell j-td">721物料001---</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="66beb548f7f27300010890db" title="" class="tb-cell j-td">345.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="66beb548f7f27300010890db" title="" class="tb-cell j-td">是</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="66beb548f7f27300010890db" title="红豆生南国，春来发几枝。" class="tb-cell j-td">红豆生南国，春来发几枝。</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="66beb548f7f27300010890db" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="66beb548f7f27300010890db" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="66beb548f7f27300010890db" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="66beb548f7f27300010890db" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="66beb548f7f27300010890db" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="66beb548f7f27300010890db" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="66beb548f7f27300010890db" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="16" data-id="66bca3cdf7f2730001e9ff89" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="66bca3cdf7f2730001e9ff89" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="66bca3cdf7f2730001e9ff89" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="66bca3cdf7f2730001e9ff89" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="66bca3cdf7f2730001e9ff89" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="66bca3cdf7f2730001e9ff89" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="66bca3cdf7f2730001e9ff89" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="66bca3cdf7f2730001e9ff89" title="导入10000" class="tb-cell j-td">导入10000</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="66bca3cdf7f2730001e9ff89" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="66bca3cdf7f2730001e9ff89" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="66bca3cdf7f2730001e9ff89" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="66bca3cdf7f2730001e9ff89" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="66bca3cdf7f2730001e9ff89" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="66bca3cdf7f2730001e9ff89" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="66bca3cdf7f2730001e9ff89" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="17" data-id="66bca3cdf7f2730001e9ff88" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="66bca3cdf7f2730001e9ff88" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="66bca3cdf7f2730001e9ff88" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="66bca3cdf7f2730001e9ff88" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="66bca3cdf7f2730001e9ff88" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="66bca3cdf7f2730001e9ff88" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="66bca3cdf7f2730001e9ff88" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="66bca3cdf7f2730001e9ff88" title="导入9999" class="tb-cell j-td">导入9999</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="66bca3cdf7f2730001e9ff88" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="66bca3cdf7f2730001e9ff88" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="66bca3cdf7f2730001e9ff88" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="66bca3cdf7f2730001e9ff88" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="66bca3cdf7f2730001e9ff88" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="66bca3cdf7f2730001e9ff88" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="66bca3cdf7f2730001e9ff88" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="18" data-id="66bca3cdf7f2730001e9ff87" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="66bca3cdf7f2730001e9ff87" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="66bca3cdf7f2730001e9ff87" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="66bca3cdf7f2730001e9ff87" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="66bca3cdf7f2730001e9ff87" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="66bca3cdf7f2730001e9ff87" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="66bca3cdf7f2730001e9ff87" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="66bca3cdf7f2730001e9ff87" title="导入9998" class="tb-cell j-td">导入9998</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="66bca3cdf7f2730001e9ff87" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="66bca3cdf7f2730001e9ff87" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="66bca3cdf7f2730001e9ff87" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="66bca3cdf7f2730001e9ff87" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="66bca3cdf7f2730001e9ff87" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="66bca3cdf7f2730001e9ff87" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="66bca3cdf7f2730001e9ff87" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr><tr style="" class="tr" data-index="19" data-id="66bca3cdf7f2730001e9ff86" data-ld="loaded">
<td>
<div class="tb-cell"></div>
</td>



<td style="" class="td-field_dh25b__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_dH25b__c" data-id="66bca3cdf7f2730001e9ff86" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_uqrv1__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_uQRv1__c" data-id="66bca3cdf7f2730001e9ff86" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-field_product__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_product__c" data-id="66bca3cdf7f2730001e9ff86" title="bat1-1526837343070048324" class="tb-cell j-td td-cell__16"><a href="javascript:;" class="j-show-lookup" data-id="823095189193523200" data-apiname="ProductObj">bat1-1526837343070048324</a><a href="/XV/UI/Home#objdetail/ProductObj/823095189193523200" target="_blank" title="新頁簽打開" class="fx-icon-newtab j-icon-newtab"></a></div>


</td>



<td style="" class="td-field_reference_field__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_reference_field__c" data-id="66bca3cdf7f2730001e9ff86" title="难过的还是我" class="tb-cell j-td">难过的还是我</div>


</td>



<td style="" class="td-field_prfnv__c td-type-num">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_PrFnv__c" data-id="66bca3cdf7f2730001e9ff86" title="" class="tb-cell j-td">234.00</div>


</td>



<td style="" class="td-field_gvx7z__c">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_GVX7z__c" data-id="66bca3cdf7f2730001e9ff86" title="" class="tb-cell j-td">否</div>


</td>



<td style="" class="td-field_s3ppj__c">






<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="field_S3ppj__c" data-id="66bca3cdf7f2730001e9ff86" title="导入9997" class="tb-cell j-td">导入9997</div>


</td>



<td style="" class="td-record_type">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="record_type" data-id="66bca3cdf7f2730001e9ff86" title="" class="tb-cell j-td">預設業務類型</div>


</td>



<td style="" class="td-life_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="life_status" data-id="66bca3cdf7f2730001e9ff86" title="正常" class="tb-cell j-td">正常</div>


</td>



<td style="" class="td-data_own_department">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="data_own_department" data-id="66bca3cdf7f2730001e9ff86" title="" class="tb-cell j-td"><span data-departmentid="999998">待分配</span></div>


</td>



<td style="" class="td-out_owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="out_owner" data-id="66bca3cdf7f2730001e9ff86" title="" class="tb-cell j-td">--</div>


</td>



<td style="" class="td-lock_status">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="lock_status" data-id="66bca3cdf7f2730001e9ff86" title="未鎖定" class="tb-cell j-td">未鎖定</div>


</td>



<td style="" class="td-relevant_team">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="relevant_team" data-id="66bca3cdf7f2730001e9ff86" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>



<td style="" class="td-owner">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="owner" data-id="66bca3cdf7f2730001e9ff86" title="xjyTest004" class="tb-cell j-td">xjyTest004</div>


</td>

<td colspan="5"><div class="tb-cell"></div></td>

<td></td>
</tr></tbody>
</table>
</div>
<div class="fix-end-b" style="width: 65px; height: 680px; right: 0px;">
<table>
<thead>
<tr>

<th class="th-null column-operate" data-name="" width="64px;"></th>

</tr>
</thead>
<tbody><tr style="" class="tr" data-index="0" data-id="67e52a3a46dc8000068dcbf1" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="67e52a3a46dc8000068dcbf1" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="1" data-id="67e5219846dc8000068d6a03" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="67e5219846dc8000068d6a03" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="2" data-id="67e521a146dc8000068d6bd7" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="67e521a146dc8000068d6bd7" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="3" data-id="6717524d2743a300014af496" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="6717524d2743a300014af496" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="4" data-id="671711cb2743a3000147175a" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="671711cb2743a3000147175a" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="5" data-id="671647ee2743a3000143e105" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="671647ee2743a3000143e105" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="6" data-id="671642d72743a3000143459d" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="671642d72743a3000143459d" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="7" data-id="670a307f85821400010f6858" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a307f85821400010f6858" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="8" data-id="670a304785821400010f5d22" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a304785821400010f5d22" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="9" data-id="670a2fd585821400010f35b4" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a2fd585821400010f35b4" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="10" data-id="670a2ea485821400010f2122" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a2ea485821400010f2122" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="11" data-id="670a2e4d85821400010f1b4b" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a2e4d85821400010f1b4b" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="12" data-id="670a2dd285821400010f14ec" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a2dd285821400010f14ec" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="13" data-id="670a16e585821400010d0354" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a16e585821400010d0354" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="14" data-id="670a16ac85821400010cf834" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="670a16ac85821400010cf834" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="15" data-id="66beb548f7f27300010890db" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66beb548f7f27300010890db" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="16" data-id="66bca3cdf7f2730001e9ff89" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66bca3cdf7f2730001e9ff89" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="17" data-id="66bca3cdf7f2730001e9ff88" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66bca3cdf7f2730001e9ff88" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="18" data-id="66bca3cdf7f2730001e9ff87" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66bca3cdf7f2730001e9ff87" class="tb-cell">
&nbsp;
</div>


</td>



</tr><tr style="" class="tr" data-index="19" data-id="66bca3cdf7f2730001e9ff86" data-ld="loaded">





<td style="" class="td-null column-operate" title="">





<!--保证编译完后不换行 换行会有样式问题-->
<div data-fieldname="" data-id="66bca3cdf7f2730001e9ff86" class="tb-cell">
&nbsp;
</div>


</td>



</tr></tbody>
</table>
</div>
<div class="dt-no-data b-g-hide" style="display: none; bottom: 8px;"><p class="no-data-text"></p></div>
<div class="dt-loading b-g-hide lg" style="position: fixed; top: 709px; display: none;"></div>
</div></div>
<div class="dt-page clearfix">
<div class="page-box"></div>

<div class="dt-size md"></div>

<div class="dt-inner">
<div class="dt-page-left"></div>
<div class="dt-page-mid"></div>
<span class="about-num">約1000+條</span><div class="about-num-wrap"><span class="look-num j-look-num">精確總數</span></div><div class="dt-page-right"><div class="crm-w-pagination"><div><span class="total-num" style="display: none;">共<em>1000</em>條</span></div> <div class="pagesize-btn" style=""><span class="page-num">1/50</span><span class="about-num">+</span><i class="el-icon-arrow-down"></i> <div class="popover" style="width: 96px;"><div class="drop-content" style="height: 1400px;"><ul class="drop-list" style="transform: translateY(0px);"><li><span class="el-icon-check active"></span><p>1/50</p></li><li><span class="el-icon-check"></span><p>2/50</p></li><li><span class="el-icon-check"></span><p>3/50</p></li><li><span class="el-icon-check"></span><p>4/50</p></li><li><span class="el-icon-check"></span><p>5/50</p></li><li><span class="el-icon-check"></span><p>6/50</p></li><li><span class="el-icon-check"></span><p>7/50</p></li><li><span class="el-icon-check"></span><p>8/50</p></li><li><span class="el-icon-check"></span><p>9/50</p></li><li><span class="el-icon-check"></span><p>10/50</p></li><li><span class="el-icon-check"></span><p>11/50</p></li><li><span class="el-icon-check"></span><p>12/50</p></li><li><span class="el-icon-check"></span><p>13/50</p></li><li><span class="el-icon-check"></span><p>14/50</p></li><li><span class="el-icon-check"></span><p>15/50</p></li><li><span class="el-icon-check"></span><p>16/50</p></li><li><span class="el-icon-check"></span><p>17/50</p></li><li><span class="el-icon-check"></span><p>18/50</p></li><li><span class="el-icon-check"></span><p>19/50</p></li><li><span class="el-icon-check"></span><p>20/50</p></li><li><span class="el-icon-check"></span><p>21/50</p></li><li><span class="el-icon-check"></span><p>22/50</p></li><li><span class="el-icon-check"></span><p>23/50</p></li><li><span class="el-icon-check"></span><p>24/50</p></li><li><span class="el-icon-check"></span><p>25/50</p></li><li><span class="el-icon-check"></span><p>26/50</p></li><li><span class="el-icon-check"></span><p>27/50</p></li><li><span class="el-icon-check"></span><p>28/50</p></li><li><span class="el-icon-check"></span><p>29/50</p></li><li><span class="el-icon-check"></span><p>30/50</p></li><li><span class="el-icon-check"></span><p>31/50</p></li><li><span class="el-icon-check"></span><p>32/50</p></li><li><span class="el-icon-check"></span><p>33/50</p></li><li><span class="el-icon-check"></span><p>34/50</p></li><li><span class="el-icon-check"></span><p>35/50</p></li><li><span class="el-icon-check"></span><p>36/50</p></li><li><span class="el-icon-check"></span><p>37/50</p></li><li><span class="el-icon-check"></span><p>38/50</p></li><li><span class="el-icon-check"></span><p>39/50</p></li><li><span class="el-icon-check"></span><p>40/50</p></li><li><span class="el-icon-check"></span><p>41/50</p></li><li><span class="el-icon-check"></span><p>42/50</p></li><li><span class="el-icon-check"></span><p>43/50</p></li><li><span class="el-icon-check"></span><p>44/50</p></li><li><span class="el-icon-check"></span><p>45/50</p></li><li><span class="el-icon-check"></span><p>46/50</p></li><li><span class="el-icon-check"></span><p>47/50</p></li><li><span class="el-icon-check"></span><p>48/50</p></li><li><span class="el-icon-check"></span><p>49/50</p></li><li><span class="el-icon-check"></span><p>50/50</p></li></ul></div> <div style="border-top: 1px solid rgba(140, 149, 168, 0.3); display: none;"><button type="button" class="el-button fx-button el-tooltip item el-button--default" aria-describedby="el-tooltip-8968" tabindex="0"><!----> <!----> <span><i class="fx-icon-question"></i>無法顯示更多</span></button></div></div></div> <div style="display: flex;"><span class="el-icon-arrow-left cs disable"></span> <span class="el-icon-arrow-right cs"></span></div></div></div>
</div>
</div>
</div></div></div></div></div>