# Confluence内容插入助手 - 油猴脚本

这是一个用于Confluence页面的油猴脚本，允许您在查看态下直接插入内容，通过个人访问令牌调用Confluence REST API实现。

## 功能特性

- ✅ 在Confluence页面查看态下插入内容
- ✅ 支持多种内容格式（存储格式、Wiki标记、纯文本）
- ✅ 支持多种插入位置（页面开头、末尾、替换全部）
- ✅ 内置常用模板（会议记录、任务清单、代码块等）
- ✅ 配置管理和连接测试
- ✅ 内容预览功能
- ✅ 响应式UI设计

## 安装步骤

### 1. 安装油猴扩展
首先确保您的浏览器已安装Tampermonkey扩展：
- [Chrome扩展商店](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- [Firefox扩展商店](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
- [Edge扩展商店](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd)

### 2. 安装脚本
1. 复制 `confluenceHelper.js` 文件的全部内容
2. 打开Tampermonkey管理面板
3. 点击"创建新脚本"
4. 粘贴代码并保存

### 3. 获取Confluence API令牌
1. 登录您的Atlassian账户
2. 访问 [API令牌管理页面](https://id.atlassian.com/manage-profile/security/api-tokens)
3. 点击"创建API令牌"
4. 输入标签名称（如"Confluence Helper"）
5. 复制生成的令牌（请妥善保存，只显示一次）

## 使用方法

### 1. 首次配置
1. 访问任意Confluence页面
2. 点击右上角的"Confluence助手"按钮
3. 在"配置"标签页中填写：
   - **Confluence基础URL**: 您的Confluence实例URL（如：https://your-domain.atlassian.net）
   - **邮箱地址**: 您的Atlassian账户邮箱
   - **API令牌**: 刚才获取的API令牌
   - **空间键**: 通常会自动检测，也可手动输入
   - **页面ID**: 自动从当前页面URL提取
4. 点击"保存配置"
5. 点击"测试连接"验证配置是否正确

### 2. 插入内容
1. 切换到"插入内容"标签页
2. 选择插入位置：
   - **页面末尾**: 在现有内容后添加
   - **页面开头**: 在现有内容前添加
   - **替换全部内容**: 完全替换页面内容（谨慎使用）
3. 选择内容格式：
   - **Confluence存储格式**: 推荐，支持完整的Confluence功能
   - **Wiki标记**: 简单的标记语法
   - **纯文本**: 普通文本
4. 在文本框中输入要插入的内容
5. 可选：点击"预览"查看格式化后的效果
6. 点击"插入内容"完成操作

### 3. 使用模板
1. 切换到"模板"标签页
2. 从下拉菜单选择预设模板：
   - **会议记录**: 标准会议记录格式
   - **任务清单**: 待办事项列表
   - **代码块**: 代码展示块
   - **信息面板**: Confluence信息宏
   - **表格**: 基础表格结构
3. 点击"使用此模板"将模板内容加载到插入区域
4. 根据需要修改内容后插入

## 支持的内容格式

### Confluence存储格式示例
```html
<h2>标题</h2>
<p>这是一个段落。</p>
<ul>
<li>列表项1</li>
<li>列表项2</li>
</ul>
```

### Wiki标记示例
```
## 标题
这是一个段落。
* 列表项1
* 列表项2
```

## 注意事项

1. **权限要求**: 确保您的账户对目标页面有编辑权限
2. **API限制**: Atlassian对API调用有频率限制，避免频繁操作
3. **内容备份**: 重要内容建议先备份，特别是使用"替换全部内容"功能时
4. **网络安全**: API令牌具有高权限，请妥善保管，不要分享给他人
5. **浏览器兼容**: 建议使用现代浏览器（Chrome、Firefox、Edge等）

## 故障排除

### 连接失败
- 检查Confluence URL是否正确（不要包含/wiki/后缀）
- 确认邮箱地址和API令牌正确
- 检查网络连接

### 插入失败
- 确认对当前页面有编辑权限
- 检查页面ID是否正确识别
- 尝试刷新页面重新获取页面信息

### 内容格式问题
- 使用"预览"功能检查格式
- 对于复杂内容，建议使用Confluence存储格式
- 参考Confluence官方文档了解存储格式语法

## 更新日志

### v1.0 (2024-01-01)
- 初始版本发布
- 支持基本的内容插入功能
- 内置常用模板
- 配置管理和连接测试

## 技术支持

如遇到问题或有功能建议，请：
1. 检查浏览器控制台是否有错误信息
2. 确认Confluence和浏览器版本
3. 提供详细的错误描述和复现步骤

## 许可证

本脚本仅供学习和个人使用，请遵守您所在组织的IT政策和Atlassian的服务条款。
