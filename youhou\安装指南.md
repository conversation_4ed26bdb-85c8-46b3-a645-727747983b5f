# Confluence内容插入助手 - 安装指南

## 📋 准备工作

### 1. 浏览器扩展
确保已安装Tampermonkey扩展：
- **Chrome**: [Chrome网上应用店](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox**: [Firefox附加组件](https://addons.mozilla.org/zh-CN/firefox/addon/tampermonkey/)
- **Edge**: [Edge加载项](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd)

### 2. Confluence API令牌
1. 登录Atlassian账户
2. 访问 https://id.atlassian.com/manage-profile/security/api-tokens
3. 点击"创建API令牌"
4. 输入标签（如"Confluence Helper"）
5. **重要**: 复制并保存令牌（只显示一次）

## 🚀 安装步骤

### 方式一：完整版安装
1. 打开Tampermonkey管理面板
2. 点击"添加新脚本"
3. 删除默认内容
4. 复制 `confluenceHelper.js` 的全部内容并粘贴
5. 按 `Ctrl+S` 保存脚本

### 方式二：精简版安装（推荐新手）
1. 打开Tampermonkey管理面板
2. 点击"添加新脚本"
3. 删除默认内容
4. 复制 `confluenceHelper-lite.js` 的全部内容并粘贴
5. 按 `Ctrl+S` 保存脚本

## ⚙️ 首次配置

### 完整版配置
1. 访问任意Confluence页面
2. 点击右上角"Confluence助手"按钮
3. 在"配置"标签页填写：
   - **基础URL**: `https://your-domain.atlassian.net`
   - **邮箱**: 您的Atlassian账户邮箱
   - **API令牌**: 刚才获取的令牌
4. 点击"保存配置"
5. 点击"测试连接"验证

### 精简版配置
1. 访问任意Confluence页面
2. 点击右上角"快速插入"按钮
3. 在弹窗中填写配置信息
4. 输入要插入的内容
5. 点击"保存并插入"

## 🎯 使用示例

### 插入简单文本
```html
<p>这是一段新增的文本内容。</p>
```

### 插入标题和列表
```html
<h2>新增章节</h2>
<ul>
<li>要点一</li>
<li>要点二</li>
<li>要点三</li>
</ul>
```

### 插入表格
```html
<table>
<tr><th>项目</th><th>状态</th><th>负责人</th></tr>
<tr><td>任务A</td><td>进行中</td><td>张三</td></tr>
<tr><td>任务B</td><td>已完成</td><td>李四</td></tr>
</table>
```

### 插入代码块
```html
<ac:structured-macro ac:name="code">
<ac:parameter ac:name="language">javascript</ac:parameter>
<ac:plain-text-body><![CDATA[
function hello() {
    console.log('Hello, World!');
}
]]></ac:plain-text-body>
</ac:structured-macro>
```

### 插入信息面板
```html
<ac:structured-macro ac:name="info">
<ac:rich-text-body>
<p>这是一个重要提示信息。</p>
</ac:rich-text-body>
</ac:structured-macro>
```

## 🔧 故障排除

### 脚本未加载
**症状**: 页面上看不到助手按钮
**解决方案**:
1. 检查Tampermonkey是否启用
2. 确认脚本状态为"启用"
3. 检查页面URL是否匹配脚本规则
4. 刷新页面重试

### 连接测试失败
**症状**: 点击"测试连接"显示失败
**解决方案**:
1. 确认基础URL格式正确（不包含/wiki/）
2. 检查邮箱地址是否正确
3. 验证API令牌是否有效
4. 确认网络连接正常

### 插入内容失败
**症状**: 点击插入后显示错误
**解决方案**:
1. 确认对当前页面有编辑权限
2. 检查页面ID是否正确识别
3. 验证内容格式是否正确
4. 尝试插入简单文本测试

### 页面ID未识别
**症状**: 配置中页面ID为空
**解决方案**:
1. 确认当前在Confluence页面（不是空间首页）
2. 手动从URL中提取页面ID
3. URL格式: `.../pages/123456/...` 中的123456就是页面ID

## 📝 注意事项

### 安全提醒
- ⚠️ API令牌具有高权限，请妥善保管
- ⚠️ 不要在公共场所或他人面前输入令牌
- ⚠️ 定期更换API令牌

### 使用建议
- 💡 重要内容建议先在测试页面验证
- 💡 使用"替换全部内容"前请备份原内容
- 💡 复杂格式建议使用预览功能检查
- 💡 遵守组织的IT安全政策

### 性能考虑
- 📊 避免频繁调用API（有速率限制）
- 📊 大量内容建议分批插入
- 📊 定期清理不需要的配置数据

## 🆘 获取帮助

### 检查清单
在寻求帮助前，请确认：
- [ ] Tampermonkey已正确安装并启用
- [ ] 脚本已保存并启用
- [ ] 页面URL匹配脚本规则
- [ ] API令牌有效且权限充足
- [ ] 网络连接正常

### 调试信息
如需技术支持，请提供：
1. 浏览器类型和版本
2. Tampermonkey版本
3. Confluence实例类型（Cloud/Server）
4. 具体错误信息
5. 浏览器控制台日志

### 常用调试命令
在浏览器控制台中执行：
```javascript
// 检查脚本是否加载
console.log('Confluence Helper loaded:', typeof window.togglePanel !== 'undefined');

// 检查配置
console.log('Config:', GM_getValue('confluence_helper_config', '{}'));

// 检查页面信息
console.log('Page ID:', document.location.href.match(/pages\/(\d+)/)?.[1]);
```

## 📚 更多资源

- [Confluence REST API文档](https://developer.atlassian.com/cloud/confluence/rest/intro/)
- [Confluence存储格式指南](https://confluence.atlassian.com/doc/confluence-storage-format-790796544.html)
- [Tampermonkey文档](https://www.tampermonkey.net/documentation.php)

---

**版本**: v1.0  
**更新日期**: 2024-01-01  
**兼容性**: Confluence Cloud, Confluence Server 7.0+
