[{"methodName": "Fx.erpdss.proxyRequest", "description": "通过集成平台访问外部Http请求，由集成平台控制访问权限。", "socketTimeOut": 10000, "ownerId": "1000", "url": "${variables_erp_sync.webUrl}/inner/erp/syncdata/func/api/proxyRequest", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "url", "description": "访问的目标url，如果有，需要带上requestParam", "type": "String"}, {"name": "method", "description": "请求方法", "type": "Map"}, {"name": "headers", "description": "请求的header，Map<String, List<String>>", "type": "Map"}, {"name": "body", "description": "请求的body，可为空", "type": "String"}, {"name": "contentType", "description": "请求的content类型，默认为application/json", "type": "String"}, {"name": "requestBodyNeedBase64Decode", "description": "请求的body是否需要使用base64解码为byte[]", "type": "Boolean"}, {"name": "responseBodyNeedBase64Encode", "description": "返回值的body是否需要使用base64编码为string", "type": "Boolean"}]}, "responseMapping": {"codeField": "errCode", "dataField": "data", "errorMsgField": "errMsg", "successCode": "s106240000", "dataReturnType": "Map", "dataReturnDescription": "请求的结果，code：状态码，message：状态信息，headers：请求头，body：内容（string类型）"}}, {"methodName": "Fx.erpdss.executeMethod", "description": "调用集成平台业务逻辑统一入口", "socketTimeOut": 120000, "ownerId": "8110", "url": "${variables_erp_sync.webUrl}/inner/erp/syncdata/customfunction/common/execute", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "type", "description": "执行的类型", "type": "String"}, {"name": "params", "description": "参数，json字符串,跟随type变化", "type": "String"}]}, "responseMapping": {"codeField": "errCode", "dataField": "data", "errorMsgField": "errMsg", "successCode": "s106240000", "dataReturnType": "String", "dataReturnDescription": "json字符串"}}, {"methodName": "Fx.erpdss.manualSyncCrm2Erp", "description": "集成平台触发单条数据同步（异步）", "socketTimeOut": 120000, "ownerId": "8110", "url": "${variables_erp_sync.webUrl}/inner/erp/syncdata/customfunction/actioncenter/manualSyncCrm2Erp", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "crmObjectApiName", "description": "集成流源对象apiName", "type": "String"}, {"name": "crmDataId", "description": "源数据id", "type": "String"}, {"name": "erpObjectApiName", "description": "集成流目标对象apiName", "type": "String"}]}, "responseMapping": {"codeField": "errCode", "dataField": "data", "errorMsgField": "errMsg", "successCode": "s106240000", "dataReturnType": "Void", "dataReturnDescription": ""}}, {"methodName": "Fx.erpdss.fsKnowledgeProxy", "description": "搜索集成平台知识库", "socketTimeOut": 300000, "ownerId": "8017", "url": "${variables_erp_sync.webUrl}/inner/erp/syncdata/func/api/fsKnowledgeProxy", "requestMapping": {"header": [{"name": "x-fs-ei", "description": "企业", "type": "String", "value": "${tenantId}"}, {"name": "x-fs-userinfo", "description": "用户id", "type": "String", "value": "${userId}"}], "body": [{"name": "keyword", "description": "搜索关键词", "type": "String"}]}, "responseMapping": {"codeField": "errCode", "dataField": "data", "errorMsgField": "errMsg", "successCode": "s106240000", "dataReturnType": "Map", "dataReturnDescription": "knowledgeContentList: 知识记录内容列表"}}]