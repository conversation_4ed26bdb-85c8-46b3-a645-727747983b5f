# JSON到Excel数据提取工具

这个工具可以从JSON文件中提取数据并导出到Excel文件。支持单个文件处理和批量处理。

## 功能特点

- 从JSON文件中提取`rows`数据并导出到Excel
- 使用JSON中的`column_list`作为Excel的列名
- 支持处理单个JSON文件或目录中的所有JSON文件
- 提供详细的处理进度和错误信息

## 使用方法

### 单个文件处理

使用`json_to_excel.py`脚本处理单个JSON文件：

```
python json_to_excel.py <json文件路径> [excel文件路径]
```

示例：

```
python json_to_excel.py account.json account.xlsx
```

如果不指定Excel文件路径，将使用与JSON文件相同的名称（但扩展名为.xlsx）。

### 批量处理

使用`json_to_excel_batch.py`脚本处理目录中的所有JSON文件：

```
python json_to_excel_batch.py --dir <目录路径>
```

示例：

```
python json_to_excel_batch.py --dir .
```

也可以用于处理单个文件：

```
python json_to_excel_batch.py <json文件路径> [excel文件路径]
```

## 注意事项

- JSON文件必须包含`rows`字段，该字段应为二维数组
- 如果JSON文件包含`column_list`字段，将使用它作为Excel的列名
- 如果没有`column_list`字段，将使用默认列名（Column_0, Column_1, ...）
- 脚本需要安装pandas库，可以通过`pip install pandas`安装