[{"definition": {"actionApiName": "ERPDSSKnowledgeAction", "functionApiName": "Fx.erpdss.fsKnowledgeProxy", "name": "集成平台知识库", "description": "搜索纷享集成平台的知识库", "icon": "fx-icon-app49", "iconBackground": "#1AC87C", "runType": "bizapi", "layout": {"input": [{"apiName": "keyword", "label": "关键词", "compositeType": "ActionInput", "required": true}]}, "extend": {"ai": {"layout": {"extendInput": [{"apiName": "intent", "label": "_T(pass.ai.extendInput.intent)", "required": true, "compositeType": "ActionTextarea"}, {"apiName": "confirmable", "label": "_T(pass.ai.extendInput.confirmable)", "required": true, "compositeType": "ActionRadio", "value": false, "options": [{"label": "_T(pass.ai.extendInput.confirmable.true)", "value": true}, {"label": "_T(pass.ai.extendInput.confirmable.false)", "value": false}]}], "extendOutput": [{"apiName": "outputFieldName", "label": "_T(pass.ai.extendOutput.outputFieldName)", "required": true, "compositeType": "ActionInput", "value": "result", "disabled": true}, {"apiName": "outputType", "label": "_T(pass.ai.extendOutput.outputType)", "required": true, "compositeType": "ActionInput", "value": "object", "disabled": true}, {"apiName": "outputInstruction", "label": "_T(pass.ai.extendOutput.outputInstruction)", "toolTip": "_T(pass.ai.extendOutput.outputInstruction.tooltip)", "compositeType": "ActionInput", "required": true}]}}, "flow": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "集成平台知识库搜索", "type": "object", "properties": {"keyword": {"type": "string", "description": "搜索关键词"}, "pageSize": {"type": "integer", "description": "每页数量", "default": 10}, "pageNum": {"type": "integer", "description": "页码", "default": 1}}, "required": ["keyword"], "additionalProperties": false}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"total": {"type": "integer", "title": "Integer", "description": "总记录数"}, "list": {"type": "array", "title": "Array", "description": "知识库列表", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "知识库ID"}, "title": {"type": "string", "description": "标题"}, "content": {"type": "string", "description": "内容"}, "url": {"type": "string", "description": "链接地址"}, "createTime": {"type": "string", "description": "创建时间"}}}}}}}, {"definition": {"actionApiName": "PromptAction", "functionApiName": "Fx.AI.completionsByPrompt", "name": "_T(action.bus.Fx.ai.completionsByPrompt.name)", "description": "_T(action.bus.Fx.ai.completionsByPrompt.description)", "icon": "fx-icon-app49", "runType": "bizapi", "iconBackground": "#1AC87C", "layout": {"input": [{"apiName": "apiName", "compositeType": "PromptApiName", "label": "_T(action.bus.Fx.ai.completionsByPrompt.promptApiName)"}, {"apiName": "sceneVariables", "compositeType": "DynamicSchema", "dynamicSourceType": "AIPrompt", "label": "_T(action.bus.Fx.ai.completionsByPrompt.sceneVariables)", "hideLabel": true, "argument": {"apiName": "${input.apiName}"}}]}, "extend": {"ai": {"layout": {"extendInput": [{"apiName": "intent", "label": "_T(pass.ai.extendInput.intent)", "required": true, "compositeType": "ActionTextarea"}, {"apiName": "confirmable", "label": "_T(pass.ai.extendInput.confirmable)", "required": true, "compositeType": "ActionRadio", "value": false, "options": [{"label": "_T(pass.ai.extendInput.confirmable.true)", "value": true}, {"label": "_T(pass.ai.extendInput.confirmable.false)", "value": false}]}], "extendOutput": [{"apiName": "outputFieldName", "label": "_T(pass.ai.extendOutput.outputFieldName)", "required": true, "compositeType": "ActionInput", "value": "result", "disabled": true}, {"apiName": "outputType", "label": "_T(pass.ai.extendOutput.outputType)", "required": true, "compositeType": "PromptOutputType", "value": "object", "disabled": true, "dependency": "${input.sceneVariables}"}, {"apiName": "outputInstruction", "label": "_T(pass.ai.extendOutput.outputInstruction)", "tooltip": "_T(pass.ai.extendOutput.outputInstruction.tooltip)", "compositeType": "ActionInput", "required": true}, {"apiName": "output", "hideLabel": true, "compositeType": "ActionDefineVariables", "dynamicSourceType": "AIPrompt", "argument": {"apiName": "${input.apiName}"}, "hidden": "{{ formData.output.outputType && formData.output.outputType !== 'object'}}", "required": true}], "output": [{"apiName": "output", "compositeType": "AIPromptOutput", "dependency": "${input.sceneVariables}"}]}}, "flow": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "提示词", "type": "object", "properties": {"apiName": {"type": "string", "description": "提示词ApiName"}, "objectData": {"type": "object", "description": "数据信息"}, "bindObjectDataId": {"type": "string", "description": "数据id"}, "otherObjectData": {"type": "array", "description": "其他数据信息"}, "sceneVariables": {"type": "object", "description": "输入"}}, "additionalProperties": false, "required": ["promptApiName"]}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "title": "String", "description": "AI回答的内容"}, "type": {"type": "string", "title": "String", "description": "返回值的类型"}}}}, {"definition": {"actionApiName": "AplAction", "functionApiName": "Fx.function.executeFunc", "name": "_T(action.bus.Fx.function.executeFunc.name)", "description": "_T(action.bus.Fx.function.executeFunc.description)", "icon": "fx-icon-obj-app32", "iconBackground": "#FFCA2B", "layout": {"input": [{"apiName": "functionApiName", "label": "_T(action.bus.Fx.function.executeFunc.functionApiName)", "compositeType": "FunctionApiName"}, {"apiName": "param", "label": "_T(action.bus.Fx.function.executeFunc.param)", "compositeType": "DynamicSchema", "dynamicSourceType": "FunctionParams", "argument": {"apiName": "${input.functionApiName}"}, "hideLabel": true}]}, "extend": {"ai": {"layout": {"extendInput": [{"apiName": "intent", "label": "_T(pass.ai.extendInput.intent)", "required": true, "compositeType": "ActionTextarea"}, {"apiName": "confirmable", "label": "_T(pass.ai.extendInput.confirmable)", "required": true, "compositeType": "ActionRadio", "value": false, "options": [{"label": "_T(pass.ai.extendInput.confirmable.true)", "value": true}, {"label": "_T(pass.ai.extendInput.confirmable.false)", "value": false}]}], "extendOutput": [{"apiName": "outputFieldName", "label": "_T(pass.ai.extendOutput.outputFieldName)", "required": true, "compositeType": "ActionInput", "value": "result", "disabled": true}, {"apiName": "outputType", "label": "_T(pass.ai.extendOutput.outputType)", "required": true, "compositeType": "ActionInput", "value": "object", "disabled": true}, {"apiName": "outputInstruction", "label": "_T(pass.ai.extendOutput.outputInstruction)", "toolTip": "_T(pass.ai.extendOutput.outputInstruction.tooltip)", "compositeType": "ActionInput", "required": true}, {"apiName": "output", "hideLabel": true, "compositeType": "ActionDefineVariables", "dynamicSourceType": "AIPrompt", "argument": {"apiName": "${input.functionApiName}"}, "varMode": "input", "required": true}], "output": [{"apiName": "FunctionParams", "compositeType": "FunctionParams", "argument": {"apiName": "${input.functionApiName}"}}]}}, "flow": {}, "function": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "类型", "type": "object", "properties": {"functionApiName": {"type": "string", "description": "需要执行的函数apiName"}, "param": {"type": "object", "description": "执行函数时传入的参数"}}, "required": ["functionApiName"], "additionalProperties": false}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {}}}, {"definition": {"actionApiName": "FxMessageSendNotice", "functionApiName": "Fx.message.sendNotice", "functionApi": "com.fxiaoke.functions.api.MessageAPI", "runType": "predefine", "name": "_T(action.bus.Fx.message.sendNotice.name)", "description": "_T(action.bus.Fx.message.sendNotice.description)", "icon": "fx-icon-obj-app310", "iconBackground": "#1AC87C", "finalAction": true, "layout": {"input": [{"apiName": "receiverIds", "label": "_T(action.bus.Fx.message.sendNotice.receiverIds)", "compositeType": "ActionSelector"}, {"apiName": "title", "length": 200, "label": "_T(action.bus.Fx.message.sendNotice.title)", "compositeType": "ActionTextareaVariable", "returnAble": false}, {"apiName": "content", "length": 500, "label": "_T(action.bus.Fx.message.sendNotice.content)", "compositeType": "ActionTextareaVariable"}], "output": []}, "extend": {"flow": {"layout": {"input": [{"apiName": "title", "maxLength": 200, "label": "_T(action.bus.Fx.message.sendNotice.flow.title)", "compositeType": "ActionTextareaVariable", "returnAble": false, "variableList": 20, "showWordLimit": true, "useFlowVariable": true, "placeholder": "_T(flow.bpm.oneFlowAction.title_plaseHolder)"}, {"apiName": "content", "maxLength": 500, "label": "_T(action.bus.Fx.message.sendNotice.flow.content)", "compositeType": "ActionTextareaVariable", "variableList": 20, "showWordLimit": true, "useFlowVariable": true, "placeholder": "_T(flow.bpm.oneFlowAction.content_plaseHolder)"}, {"apiName": "receiverIds", "useFlowVariable": true, "tabOpts": {"member": true, "group": true, "usergroup": true}, "addBtnLabel": "_T(flow.bpm.oneFlowAction.receiverIds_plaseHolder)"}]}}, "ai": {"layout": {"input": [{"apiName": "title", "hideLabel": true, "defaultValue": {"type": "string", "source": "llm", "description": "_T(action.bus.Fx.message.sendNotice.title.defaultValue.description)", "required": true}}, {"apiName": "content", "hideLabel": true, "defaultValue": {"type": "string", "source": "llm", "description": "_T(action.bus.Fx.message.sendNotice.content.defaultValue.description)", "required": true}}, {"apiName": "receiverIds", "hideLabel": true, "defaultValue": {"type": "array", "source": "llm", "description": "_T(action.bus.Fx.message.sendNotice.receiverIds.defaultValue.description)", "required": true}}], "extendInput": [{"apiName": "intent", "label": "_T(pass.ai.extendInput.intent)", "required": true, "compositeType": "ActionTextarea"}, {"apiName": "confirmable", "label": "_T(pass.ai.extendInput.confirmable)", "required": true, "compositeType": "ActionRadio", "value": false, "options": [{"label": "_T(pass.ai.extendInput.confirmable.true)", "value": true}, {"label": "_T(pass.ai.extendInput.confirmable.false)", "value": false}]}], "extendOutput": [{"apiName": "outputFieldName", "label": "_T(pass.ai.extendOutput.outputFieldName)", "required": true, "compositeType": "ActionInput", "value": "result", "disabled": true}, {"apiName": "outputType", "label": "_T(pass.ai.extendOutput.outputType)", "required": true, "compositeType": "ActionInput", "value": "boolean", "disabled": true}, {"apiName": "outputInstruction", "label": "_T(pass.ai.extendOutput.outputInstruction)", "toolTip": "_T(pass.ai.extendOutput.outputInstruction.tooltip)", "compositeType": "ActionInput", "value": "_T(pass.ai.extendOutput.outputInstruction.value)", "disabled": true, "required": true}]}}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "发送CRM提醒", "type": "object", "properties": {"title": {"type": "string", "title": "String", "dataType": "String", "description": "CRM提醒标题"}, "content": {"type": "string", "title": "String", "dataType": "String", "description": "CRM提醒内容"}, "receiverIds": {"type": "array", "title": "List", "dataType": "List", "description": "crm提醒接收人", "items": {"type": "string"}}, "notice": {"type": "object", "title": "Notice", "description": "关联数据", "extend": {"className": "com.fxiaoke.functions.model.Notice", "defaultValue": "{\"type\":0}"}}}, "required": ["title", "content", "receiverIds"], "additionalProperties": false}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "类型", "type": "object", "additionalProperties": false}}, {"definition": {"actionApiName": "FxBpmStart", "functionApiName": "Fx.bpm.start", "functionApi": "com.fxiaoke.functions.api.BpmAPI", "runType": "predefine", "name": "_T(action.bus.Fx.bpm.start.name)", "description": "_T(action.bus.Fx.bpm.start.description)", "icon": "fx-icon-obj-app32", "iconBackground": "#FFCA2B", "layout": {"input": [{"apiName": "entityId", "label": "_T(action.bus.Fx.bpm.start.entityId)", "compositeType": "DynamicSchema", "dynamicSourceType": "ObjectApiNames"}, {"apiName": "sourceWorkflowId", "label": "_T(action.bus.Fx.bpm.start.sourceWorkflowId)", "compositeType": "DynamicSchema", "dynamicSourceType": "BpmDefinitions", "bizArgument": {"apiName": "${input.entityId}"}}, {"apiName": "objectId", "label": "_T(action.bus.Fx.bpm.start.objectId)", "compositeType": "Text"}], "output": []}, "extend": {"flow": {}, "ai": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "title": "触发业务流程,通过sourceWorkflowId触发", "properties": {"sourceWorkflowId": {"type": "string", "title": "String", "description": "业务流sourceWorkflowId"}, "objectId": {"type": "string", "title": "String", "description": "对象数据Id"}, "entityId": {"type": "string", "title": "String", "description": "对象ApiName"}}, "additionalProperties": false, "required": ["sourceWorkflowId", "objectId", "entityId"]}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"isError": {"type": "boolean", "title": "Boolean", "description": "是否报错"}, "data": {"type": "string", "title": "String", "description": "触发的实例Id"}, "message": {"type": "string"}}, "additionalProperties": false, "required": ["isError", "data", "message"]}}, {"definition": {"actionApiName": "TransferToHuman", "functionApiName": "Fx.AI.TransferToHuman", "name": "智能转人工客服", "description": "智能转人工客服", "runType": "bizapi", "icon": "fx-icon-app49", "iconBackground": "#1AC87C", "layout": {"input": [{"apiName": "sessionId", "compositeType": "ActionTextareaVariable", "label": "客服会话ID"}], "output": [{"apiName": "tip", "compositeType": "ActionTextareaVariable", "label": "转人工提示语"}]}, "extend": {"ai": {"layout": {"input": [{"apiName": "sessionId", "hideLabel": true}]}}, "flow": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "转人工客服", "type": "object", "properties": {"sessionId": {"type": "string", "title": "String", "dataTYpe": "String", "description": "客服会话ID"}}, "additionalProperties": false, "required": ["sessionId"]}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"tip": {"type": "string", "dataType": "String", "description": "转接进行中的提示语"}}}}, {"definition": {"actionApiName": "KnowledgeAction", "functionApiName": "Fx.AI.knowledgeSearch", "name": "_T(action.bus.Fx.ai.knowledgeSearch.name)", "description": "_T(action.bus.Fx.ai.knowledgeSearch.description)", "runType": "bizapi", "icon": "fx-icon-app49", "iconBackground": "#1AC87C", "layout": {"input": [{"apiName": "scene", "compositeType": "ServiceKnowledgeObjScenary", "label": "_T(action.bus.Fx.AI.knowledgeSearch.scene)"}, {"apiName": "content", "compositeType": "ActionTextareaVariable", "label": "_T(action.bus.Fx.AI.knowledgeSearch.content)"}], "output": [{"apiName": "chatGPTReply<PERSON><PERSON>nt", "label": "_T(action.bus.Fx.AI.knowledgeSearch.chatGPTReplyContent)"}, {"apiName": "searchResult", "label": "_T(action.bus.Fx.AI.knowledgeSearch.searchResult)"}]}, "extend": {"ai": {"layout": {"input": [{"apiName": "scene"}, {"apiName": "content", "hideLabel": true}]}}, "flow": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "知识库", "type": "object", "properties": {"scene": {"type": "string", "title": "String", "dataTYpe": "String", "description": "知识库范围"}, "content": {"type": "object", "title": "String", "dataType": "String", "description": "用户问题"}}, "additionalProperties": false, "required": ["scene", "content"]}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"chatGPTReplyContent": {"type": "string", "dataType": "String", "description": "AI回复"}, "searchResult": {"type": "array", "description": "相关资料", "dataType": "List", "items": {"type": "object", "properties": {"dataId": {"type": "string", "description": "知识库数据id"}, "title": {"type": "string", "description": "知识库标题"}, "url": {"type": "string", "description": "文章url"}}, "additionalProperties": false, "required": ["dataId", "title", "url"]}}}}}, {"definition": {"actionApiName": "RAGAction", "functionApiName": "Fx.AI.RAGRetrieval", "name": "_T(action.bus.Fx.AI.RAG.retrieval.name)", "description": "_T(action.bus.Fx.AI.RAG.retrieval.description)", "runType": "bizapi", "icon": "fx-icon-bi<PERSON>ji", "iconBackground": "#FF9B29", "layout": {"input": [{"apiName": "ragApiName", "compositeType": "RagApiName", "label": "RAG Index ApiName"}, {"apiName": "query", "compositeType": "ActionTextareaVariable", "label": "_T(action.bus.Fx.AI.RAG.retrieval.content)"}], "output": [{"apiName": "scoreThreshold", "label": "_T(action.bus.Fx.AI.RAG.retrieval.scoreThreshold)"}, {"apiName": "hits", "label": "_T(action.bus.Fx.AI.RAG.retrieval.hits)"}]}, "extend": {"ai": {"layout": {"input": [{"apiName": "scene"}, {"apiName": "content", "hideLabel": true}]}}, "flow": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "知识库", "type": "object", "properties": {"ragApiName": {"type": "string", "title": "String", "dataType": "String", "description": "RAG Index"}, "query": {"type": "object", "title": "String", "dataType": "String", "description": "用户问题"}}, "additionalProperties": false, "required": ["ragApiName", "query"]}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"scoreThreshold": {"type": "number", "dataType": "Float", "description": "得分阈值"}, "hits": {"type": "array", "dataType": "List", "items": {"type": "object", "properties": {"objectApiName": {"type": "string", "description": "对象apiName"}, "id": {"type": "string", "description": "对象数据id"}, "content": {"type": "string", "description": "文章内容"}, "retrievalType": {"type": "string", "description": "召回类型"}, "score": {"type": "number", "description": "得分"}}, "additionalProperties": false, "required": ["scoreThreshold", "hits"]}}}}}, {"definition": {"actionApiName": "ERPSyncAction", "functionApiName": "Fx.erpdss.manualSyncCrm2Erp", "name": "_T(action.bus.Fx.erpdss.manualSyncCrm2Erp.name)", "description": "_T(action.bus.Fx.erpdss.manualSyncCrm2Erp.description)", "icon": "fx-icon-app49", "iconBackground": "#1AC87C", "layout": {"input": [{"apiName": "crmObjectApiName", "compositeType": "ActionSelectObject", "type": "object", "label": "_T(action.bus.Fx.erpdss.manualSyncCrm2Erp.crmObjectApiName)"}, {"apiName": "erpStream", "compositeType": "ActionSelectERPStream", "label": "_T(action.bus.Fx.erpdss.manualSyncCrm2Erp.erpSteam)", "props": {"objectApiName": "{{ formData.input.crmObjectApiName }}"}}, {"apiName": "crmDataId", "compositeType": "ActionTextareaVariable", "label": "_T(action.bus.Fx.erpdss.manualSyncCrm2Erp.crmDataId)"}], "output": []}, "extend": {"ai": {}, "flow": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"crmObjectApiName": {"type": "string", "title": "String", "description": "CRM对象"}, "erpStream": {"type": "string", "title": "String", "description": "触发集成流"}, "crmDataId": {"type": "string", "title": "String", "description": "CRM数据id"}}, "additionalProperties": false, "required": ["crmObjectApiName", "erpStream", "crmDataId"]}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {}}}, {"definition": {"actionApiName": "BatchDocToMarkdownAction", "functionApiName": "Fx.file.batchDocToMarkdown", "name": "_T(action.bus.Fx.file.batchDocToMarkdown.name)", "description": "_T(action.bus.Fx.file.batchDocToMarkdown.description)", "icon": "fx-icon-app49", "runType": "bizapi", "iconBackground": "#1AC87C", "layout": {"input": [{"apiName": "docInfos", "compositeType": "ActionCascader", "type": "array", "label": "_T(action.bus.Fx.file.batchDocToMarkdown.docInfos)"}], "output": [{"apiName": "markdownInfos", "compositeType": "ActionTextareaVariable", "type": "array", "label": "_T(action.bus.Fx.file.batchDocToMarkdown.markdownInfos)"}]}, "extend": {"ai": {}, "flow": {}}}, "input": {"type": "object", "properties": {"docInfos": {"type": "array", "description": "文档列表", "items": {"type": "object", "title": "List", "properties": {"path": {"type": "string", "description": "文件Path支持N、TN文件"}, "filename": {"type": "string", "description": "文件名"}, "extension": {"type": "string", "description": "文件扩展名"}, "size": {"type": "number", "description": "文件大小"}}, "required": ["path", "filename", "extension", "size"]}}}, "required": ["docInfos"]}, "output": {"type": "object", "properties": {"markdownInfos": {"type": "array", "description": "转换过的文档列表", "items": {"type": "object", "properties": {"path": {"type": "string", "description": "文件path（一般是TN）"}, "filename": {"type": "string", "description": "文件名"}, "extension": {"type": "string", "description": "文件类型的扩展名"}, "size": {"type": "integer", "description": "文件大小"}, "outputWordCount": {"type": "integer", "description": "markdown文件的有效字符数"}, "originalDocPageCount": {"type": "integer", "description": "原始文件的页码"}}, "required": ["path", "filename", "extension", "size", "outputWordCount", "originalDocPageCount"]}}}}}, {"definition": {"actionApiName": "MarketingInsightsAction", "functionApiName": "Fx.marketing.marketingInsights", "name": "营销AI洞察", "description": "营销AI洞察", "runType": "bizapi", "icon": "fx-icon-bi<PERSON>ji", "iconBackground": "#FF9B29", "layout": {"input": [{"apiName": "visitorId", "compositeType": "ActionTextareaVariable", "label": "访客ID"}, {"apiName": "customerSessionId", "compositeType": "ActionTextareaVariable", "label": "客服会话ID"}], "output": [{"apiName": "trendsSummary", "label": "营销动态摘要"}, {"apiName": "insightOverview", "label": "画像洞察概览"}, {"apiName": "scoreModelResult", "label": "评分模型结果"}]}, "extend": {"ai": {}, "flow": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"visitorId": {"type": "String", "description": "访客id"}, "customerSessionId": {"type": "String", "description": "客服会话id"}}, "additionalProperties": false, "required": ["visitorId", "customerSessionId"]}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"trendsSummary": {"type": "string", "description": "营销动态摘要"}, "insightOverview": {"type": "string", "description": "画像洞察概览"}, "scoreModelResult": {"type": "string", "description": "评分模型结果"}}}}, {"definition": {"actionApiName": "BatchQuerySDRVariableDataAction", "functionApiName": "Fx.marketing.batchQuerySDRVariableData", "name": "sdr相关变量数据", "description": "sdr相关变量数据", "runType": "bizapi", "icon": "fx-icon-bi<PERSON>ji", "iconBackground": "#FF9B29", "layout": {"input": [{"apiName": "visitorId", "compositeType": "ActionTextareaVariable", "label": "访客id"}, {"apiName": "chatRecords", "compositeType": "ActionTextareaVariable", "label": "客服机器人会话记录"}], "output": [{"apiName": "result", "label": "执行结果"}]}, "extend": {"flow": {}, "ai": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"visitorId": {"type": "String", "description": "访客id"}, "chatRecords": {"type": "object", "description": "客服机器人会话记录"}}, "additionalProperties": false, "required": ["visitorId", "chatRecords"]}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"result": {"type": "object", "description": "结果"}}}}, {"definition": {"actionApiName": "BatchQuerySDRRelatedScriptsAction", "functionApiName": "Fx.marketing.batchQuerySDRRelatedScripts", "name": "sdr相关话术案例", "description": "sdr相关话术案例", "runType": "bizapi", "icon": "fx-icon-bi<PERSON>ji", "iconBackground": "#FF9B29", "layout": {"input": [{"apiName": "communicationActions", "compositeType": "ActionTextareaVariable", "label": "沟通动作"}, {"apiName": "intentionTypes", "compositeType": "ActionTextareaVariable", "label": "意图类型"}, {"apiName": "modelId", "compositeType": "ActionTextareaVariable", "label": "核心模型"}], "output": [{"apiName": "result", "label": "执行结果"}]}, "extend": {"flow": {}, "ai": {}}}, "input": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"communicationActions": {"type": "String", "description": "沟通动作"}, "intentionTypes": {"type": "object", "description": "意图类型"}, "modelId": {"type": "object", "description": "核心模型"}}, "additionalProperties": false, "required": ["communicationActions", "intentionTypes", "modelId"]}, "output": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"result": {"type": "object", "description": "结果"}}}}]