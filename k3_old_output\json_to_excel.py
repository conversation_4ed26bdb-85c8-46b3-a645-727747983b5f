import json
import pandas as pd
import os
import sys

def json_to_excel(json_file, excel_file=None):
    """
    从JSON文件提取数据并导出到Excel文件
    
    参数:
        json_file: JSON文件路径
        excel_file: Excel文件路径，如果为None，则使用JSON文件名作为Excel文件名
    """
    # 如果未指定Excel文件名，则使用JSON文件名
    if excel_file is None:
        excel_file = os.path.splitext(json_file)[0] + '.xlsx'
    
    # 读取JSON文件
    print(f"正在读取JSON文件: {json_file}")
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
        return False
    
    # 检查JSON文件是否包含必要的字段
    if 'rows' not in data:
        print("错误: JSON文件中没有找到'rows'字段")
        return False
    
    # 获取数据行
    rows = data['rows']
    print(f"找到 {len(rows)} 行数据")
    
    # 获取列名
    if 'column_list' in data:
        columns = data['column_list']
        print(f"使用JSON中的列名: {columns}")
    else:
        # 如果没有列名，则使用数字作为列名
        if len(rows) > 0:
            columns = [f"Column_{i}" for i in range(len(rows[0]))]
            print(f"未找到列名，使用默认列名: {columns}")
        else:
            print("错误: 没有数据行且没有列名")
            return False
    
    # 创建DataFrame
    df = pd.DataFrame(rows, columns=columns)
    
    # 导出到Excel
    print(f"正在导出数据到Excel文件: {excel_file}")
    try:
        df.to_excel(excel_file, index=False)
        print(f"成功导出数据到: {excel_file}")
        return True
    except Exception as e:
        print(f"导出Excel文件失败: {e}")
        return False

def main():
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("用法: python json_to_excel.py <json_file> [excel_file]")
        print("示例: python json_to_excel.py account.json account.xlsx")
        return
    
    # 获取JSON文件路径
    json_file = sys.argv[1]
    
    # 获取Excel文件路径（可选）
    excel_file = None
    if len(sys.argv) > 2:
        excel_file = sys.argv[2]
    
    # 执行转换
    json_to_excel(json_file, excel_file)

if __name__ == "__main__":
    main()