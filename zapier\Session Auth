let domain = bundle.authData.domain;
//domain提取/前面部分
if (domain.includes('/')) {
    domain = domain.split('/')[0];
}

if (!domain.startsWith('open.') && !domain.startsWith('open-')) {
    if (bundle.authData.domain === "www.fxiaoke.com" ||
        bundle.authData.domain === "www.sharecrm.com") {
        domain = "open.fxiaoke.com";
    } else {
        domain = 'open-' + domain;
    }
}

let baseUrl = 'https://' + domain


const options = {
    url: 'https://' + domain + '/cgi/corpAccessToken/get/V2',
    method: 'POST',
    headers: {

    },
    params: {

    },
    body: {
        'appId': bundle.authData.appId,
        'appSecret': bundle.authData.appSecret,
        'permanentCode': bundle.authData.permanentCode
    },
    removeMissingValuesFrom: {
        'body': false,
        'params': false
    },
}

return z.request(options)
    .then((response) => {
        const tokenResult = JSON.parse(response.content);

        if (tokenResult.errorCode != "0") {
            throw new Error(tokenResult.errorMessage)
        }
        const corpAccessToken = tokenResult.corpAccessToken;
        const corpId = tokenResult.corpId;

        const option = {
            url: 'https://' + domain + '/cgi/user/getByMobile',
            method: 'POST',
            headers: {},
            params: {},
            body: {
                "corpAccessToken": corpAccessToken,
                "corpId": corpId,
                "mobile": bundle.authData.phone
            },
            removeMissingValuesFrom: {
                'body': false,
                'params': false
            }
        };

        return z.request(option).then((secondResponse) => {
            const results = JSON.parse(secondResponse.content);
            if (results.errorCode != '0') {
                throw new Error('The mobile phone number is wrong or does not exist');
            }
            const openUserId = results.empList[0].openUserId;
            if (!openUserId) {
                // 如果没有找到 openUserId，则抛出异常
                throw new Error('The mobile phone number is wrong or does not exist');
            }
            const tokenData = {
                tokenResult,
                corpAccessToken: tokenResult.corpAccessToken,
                corpId: tokenResult.corpId,
                baseUrl,
                openUserId,
                domain
            };
            return tokenData;
        });
    });
