import pandas as pd
import re
import os

# 读取 grayColl.txt
with open('mongodb-coll/grayColl.txt', 'r', encoding='utf-8') as f:
    content = f.read()

# 提取所有 collectionName
collection_names = [x.strip() for x in content.split(',') if x.strip()]

# 提取 ei 的函数
def extract_ei(coll_name):
    # 去掉前缀 buf_
    if coll_name.startswith('buf_'):
        s = coll_name[4:]
        # 以第一个下划线为界
        ei = s.split('_')[0]
        return ei
    return None

# 提取所有 ei 及映射到 collectionName
all_ei = set()
ei_to_collections = {}
for name in collection_names:
    ei = extract_ei(name)
    if ei:
        all_ei.add(ei)
        if ei not in ei_to_collections:
            ei_to_collections[ei] = []
        ei_to_collections[ei].append(name)

# 读取 90dt.csv 的第二列（ei）
ei_set = set()
with open('mongodb-coll/90dt.csv', 'r', encoding='utf-8') as f:
    lines = f.readlines()
    for i, line in enumerate(lines):
        if i == 0:
            continue  # 跳过表头
        parts = line.strip().split(',')
        if len(parts) >= 2:
            ei_set.add(parts[1])

# 读取 tenant_info.xlsx，建立 tenantId 到 环境 的映射
tenant_df = pd.read_excel('mongodb-coll/tenant_info.xlsx')
tenant_env_map = {}
for idx, row in tenant_df.iterrows():
    tenant_id = str(row['tenantId'])
    env = row['环境'] if '环境' in row else ''
    tenant_env_map[tenant_id] = env

# 生成结果
result = []
no_log_collections_by_env = {}
for ei in sorted(all_ei, key=lambda x: int(x) if x.isdigit() else x):
    has_log = '是' if ei in ei_set else '否'
    env = tenant_env_map.get(ei, '')
    result.append({'ei': ei, '90天内是否有日志': has_log, '环境': env})
    if has_log == '否':
        # 记录所有没日志的ei对应的collectionName，按环境分组
        if env not in no_log_collections_by_env:
            no_log_collections_by_env[env] = []
        no_log_collections_by_env[env].extend(ei_to_collections[ei])

# 保存为 Excel
out_path = 'mongodb-coll/ei_90天日志统计.xlsx'
df = pd.DataFrame(result)
df.to_excel(out_path, index=False)

# 输出没日志的collectionName到txt，按环境分文件
for env, collections in no_log_collections_by_env.items():
    # 处理空环境名
    env_name = env if env else '未知环境'
    # 文件名中不能有非法字符
    safe_env_name = re.sub(r'[\\/:*?"<>|]', '_', str(env_name))
    no_log_path = f'mongodb-coll/no_log_collections_{safe_env_name}.txt'
    with open(no_log_path, 'w', encoding='utf-8') as f:
        f.write(','.join(collections))
    print(f'已生成 {no_log_path}')

print(f'已生成 {out_path}') 