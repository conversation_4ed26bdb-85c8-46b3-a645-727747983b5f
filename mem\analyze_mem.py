import re
import json

def parse_mem_file(file_path):
    result = {
        "summary": {},
        "categories": [],
        "virtual_memory_map": []
    }
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # 解析总览
    for i, line in enumerate(lines):
        if line.strip().startswith("Total:"):
            total_match = re.search(r"reserved=(\d+)KB, committed=(\d+)KB", line)
            if total_match:
                result["summary"]["total_reserved_KB"] = int(total_match.group(1))
                result["summary"]["total_committed_KB"] = int(total_match.group(2))
            for j in range(1, 3):
                if i + j < len(lines):
                    m = re.match(r"\s*(\w+):\s*(.*)", lines[i+j])
                    if m:
                        key = m.group(1)
                        value = m.group(2)
                        if key == "malloc":
                            malloc_match = re.search(r"(\d+)KB", value)
                            if malloc_match:
                                result["summary"]["malloc_KB"] = int(malloc_match.group(1))
                        elif key == "mmap":
                            mmap_match = re.search(r"reserved=(\d+)KB, committed=(\d+)KB", value)
                            if mmap_match:
                                result["summary"]["mmap_reserved_KB"] = int(mmap_match.group(1))
                                result["summary"]["mmap_committed_KB"] = int(mmap_match.group(2))
            break

    # 解析各类内存分配
    category_pattern = re.compile(r"^-\s+(\S[\S ]+?) \(reserved=(\d+)KB, committed=(\d+)KB.*\)")
    for line in lines:
        m = category_pattern.match(line)
        if m:
            result["categories"].append({
                "name": m.group(1).strip(),
                "reserved_KB": int(m.group(2)),
                "committed_KB": int(m.group(3))
            })

    # 解析 Virtual memory map
    vm_section = False
    vm_entry = None
    for line in lines:
        if line.strip().startswith("Virtual memory map:"):
            vm_section = True
            continue
        if vm_section:
            addr_match = re.match(r"\[(0x[0-9a-f]+) - (0x[0-9a-f]+)\] (reserved|reserved and committed|committed) (\d+)KB for ([^\n]+)", line.strip(), re.IGNORECASE)
            if addr_match:
                vm_entry = {
                    "address_start": addr_match.group(1),
                    "address_end": addr_match.group(2),
                    "type": addr_match.group(3),
                    "size_KB": int(addr_match.group(4)),
                    "desc": addr_match.group(5),
                    "stacktrace": []
                }
                result["virtual_memory_map"].append(vm_entry)
            elif vm_entry and line.strip().startswith("[") is False and line.strip():
                # 解析堆栈
                vm_entry["stacktrace"].append(line.strip())
            elif line.strip() == "":
                vm_entry = None
    return result

# 生成结论和问题

def generate_conclusion_and_issues(data):
    conclusion = ""
    issues = []
    # 总体内存使用判断
    total_reserved = data["summary"].get("total_reserved_KB", 0)
    total_committed = data["summary"].get("total_committed_KB", 0)
    if total_reserved > 8 * 1024 * 1024:
        issues.append(f"总保留内存过高: {total_reserved/1024/1024:.2f} GB")
    if total_committed > 4 * 1024 * 1024:
        issues.append(f"总已分配内存过高: {total_committed/1024/1024:.2f} GB")
    # 各类内存分配判断
    for cat in data.get("categories", []):
        name = cat["name"]
        reserved = cat["reserved_KB"]
        committed = cat["committed_KB"]
        if reserved == 0 and committed == 0:
            continue
        if reserved > 512*1024:
            issues.append(f"类别 {name} 保留内存过高: {reserved/1024:.1f} MB")
        if committed > 256*1024:
            issues.append(f"类别 {name} 已分配内存过高: {committed/1024:.1f} MB")
        if name in ["Unknown", "Other"] and (reserved > 0 or committed > 0):
            issues.append(f"存在未知或未分类内存分配: {name} {reserved/1024:.1f} MB 保留, {committed/1024:.1f} MB 已分配")
    if not issues:
        conclusion = "内存分配整体正常，无明显异常。"
    else:
        conclusion = "检测到如下内存异常或潜在问题："
    return conclusion, issues

if __name__ == "__main__":
    import sys
    input_file = "mem.txt"
    output_file = "mem_analysis.json"
    data = parse_mem_file(input_file)
    conclusion, issues = generate_conclusion_and_issues(data)
    data["conclusion"] = conclusion
    data["issues"] = issues
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"分析完成，结果已保存到 {output_file}")