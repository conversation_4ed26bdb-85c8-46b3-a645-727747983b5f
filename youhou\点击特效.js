// ==UserScript==
// @name         鼠标左击特效
// @namespace    https://greasyfork.org/zh-CN/users/219208-%E5%A4%A9%E6%B3%BD%E5%B2%81%E6%9C%88
// @version      0.0.8
// @description  鼠标左击特效，支持多种特效样式
// <AUTHOR>
// @match        *
// @include      /[a-zA-z]+://[^\s]*/
// @run-at       document_start
// @grant        unsafeWindow
// @grant        GM_setClipboard
// @grant        GM_registerMenuCommand
// @grant        GM_unregisterMenuCommand
// @grant        GM_setValue
// @grant        GM_getValue
// @downloadURL https://update.greasyfork.org/scripts/401901/%E9%BC%A0%E6%A0%87%E5%B7%A6%E5%87%BB%E7%89%B9%E6%95%88.user.js
// @updateURL https://update.greasyfork.org/scripts/401901/%E9%BC%A0%E6%A0%87%E5%B7%A6%E5%87%BB%E7%89%B9%E6%95%88.meta.js
// ==/UserScript==

// 特效配置
const effects = {
    kaomoji: {
        name: "颜表情特效",
        getText: () => {
            const kaomojis = [
                "OωO",
                "(๑•́ ∀ •̀๑)",
                "(๑•́ ₃ •̀๑)",
                "(๑•̀_•́๑)",
                "（￣へ￣）",
                "(╯°口°)╯(┴—┴",
                "૮( ᵒ̌皿ᵒ̌ )ა",
                "╮(｡>口<｡)╭",
                "( ง ᵒ̌皿ᵒ̌)ง⁼³₌₃",
                "(ꐦ°᷄д°᷅)",
                "❤🌙☀😄"
            ];
            return kaomojis[Math.floor(Math.random() * kaomojis.length)];
        }
    },
    hearts: {
        name: "爱心特效",
        getText: () => {
            const hearts = ["❤", "💖", "💝", "💕", "💗", "💓", "💞", "💟"];
            return hearts[Math.floor(Math.random() * hearts.length)];
        }
    },
    stars: {
        name: "星星特效",
        getText: () => {
            const stars = ["⭐", "🌟", "✨", "💫", "⭐", "🌟", "✨", "💫"];
            return stars[Math.floor(Math.random() * stars.length)];
        }
    },
    rainbow: {
        name: "彩虹特效",
        getText: () => {
            const colors = ["🌈", "🌈", "🌈", "🌈", "🌈", "🌈", "🌈", "🌈"];
            return colors[Math.floor(Math.random() * colors.length)];
        }
    },
    text: {
        name: "文字特效",
        getText: () => {
            const texts = ["平安喜乐", "未来可期", "所愿皆所得", "常安宁", "暴富暴美", "积极向上", "自在如风", "有趣有盼"];
            return texts[Math.floor(Math.random() * texts.length)];
        }
    }
};

// 初始化配置
let currentEffect = GM_getValue('currentEffect', 'kaomoji');
let click_cnt = 0;

// 存储菜单ID
let menuIds = [];

// 注册菜单命令
function registerMenuCommands() {
    // 清除所有已存在的菜单
    menuIds.forEach(id => GM_unregisterMenuCommand(id));
    menuIds = [];

    // 注册新的菜单
    Object.keys(effects).forEach(effectKey => {
        const menuId = GM_registerMenuCommand(
            `特效: ${effects[effectKey].name}${currentEffect === effectKey ? ' ✓' : ''}`,
            () => {
                currentEffect = effectKey;
                GM_setValue('currentEffect', effectKey);
                // 重新注册菜单以更新选中状态
                registerMenuCommands();
            }
        );
        menuIds.push(menuId);
    });
}

onload = function() {
    var $html = document.getElementsByTagName("html")[0];
    var $body = document.getElementsByTagName("body")[0];
    
    // 注册菜单命令
    registerMenuCommands();
    
    $html.onclick = function(e) {
        var $elem = document.createElement("b");
        const colors = ["#E94F06", "#d6c560", "#a8bf8f", "#88bfb8"];
        $elem.style.color = colors[Math.floor(Math.random() * colors.length)];
        $elem.style.zIndex = 9999;
        $elem.style.position = "absolute";
        $elem.style.select = "none";
        $elem.style.userSelect = "none";
        $elem.style.webkitUserSelect = "none";
        $elem.style.mozUserSelect = "none";
        $elem.style.msUserSelect = "none";
        var x = e.pageX;
        var y = e.pageY;
        $elem.style.left = (x - 10) + "px";
        $elem.style.top = (y - 20) + "px";
        clearInterval(anim);

        // 根据当前特效获取文本
        if (currentEffect === 'kaomoji') {
            $elem.innerText = effects.kaomoji.getText();
        } else {
            $elem.innerText = effects[currentEffect].getText();
            $elem.style.fontSize = "18px"; // 调整文字大小
        }

        var increase = 0;
        var anim;
        setTimeout(function() {
            anim = setInterval(function() {
                if (++increase == 150) {
                    clearInterval(anim);
                    $body.removeChild($elem);
                }
                $elem.style.top = y - 20 - increase + "px";
                $elem.style.opacity = (150 - increase) / 120;
            }, 8);
        }, 30);
        $body.appendChild($elem);
    };
};