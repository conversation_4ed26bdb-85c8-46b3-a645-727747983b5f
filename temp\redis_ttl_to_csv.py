import csv
import re

def parse_log_line(line):
    # 使用正则表达式提取信息
    pattern = r'host: ([\d\.]+) Database: (\d+), Key: ([^,]+), TTL: (-?\d+)'
    match = re.match(pattern, line.strip())
    if match:
        host, db, key, ttl = match.groups()
        return {
            'host': host,
            'database': db,
            'key': key,
            'ttl': ttl
        }
    return None

def convert_log_to_csv(input_file, output_file):
    # 定义CSV表头
    fieldnames = ['host', 'database', 'key', 'ttl']
    
    with open(input_file, 'r', encoding='utf-8') as log_file, \
         open(output_file, 'w', encoding='utf-8', newline='') as csv_file:
        
        writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
        writer.writeheader()
        
        for line in log_file:
            parsed_data = parse_log_line(line)
            if parsed_data:
                writer.writerow(parsed_data)

if __name__ == '__main__':
    input_file = 'redis_chk_ttl.log'
    output_file = 'redis_ttl_data.csv'
    convert_log_to_csv(input_file, output_file)
    print(f'转换完成！数据已保存到 {output_file}') 